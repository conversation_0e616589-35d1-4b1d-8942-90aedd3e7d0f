# Accessibility Requirements

## Compliance Target
**Standard:** WCAG 2.1 Level AA compliance for inclusive access

## Key Requirements

**Visual:**
- Color contrast ratios: 4.5:1 minimum for normal text, 3:1 for large text
- Focus indicators: Visible 2dp outline with primary color
- Text sizing: Support for system font scaling up to 200%

**Interaction:**
- Keyboard navigation: Full app navigation without touch input
- Screen reader support: Meaningful content descriptions and navigation landmarks
- Touch targets: Minimum 48dp touch targets with adequate spacing

**Content:**
- Alternative text: Descriptive alt text for culture photos and diagrams
- Heading structure: Logical H1-H6 hierarchy throughout app
- Form labels: Clear, descriptive labels for all input fields

## Testing Strategy
Regular testing with screen readers (TalkBack), high contrast modes, and large text settings. User testing with vision-impaired tissue culture practitioners.
