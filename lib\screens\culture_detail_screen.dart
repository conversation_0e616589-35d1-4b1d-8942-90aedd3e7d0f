import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/culture_detail_cubit.dart';
import '../data/database/database.dart';
import '../data/models/culture_status.dart';
import 'create_subculture_screen.dart';

class CultureDetailScreen extends StatelessWidget {
  const CultureDetailScreen({super.key, required this.cultureId});

  final String cultureId;

  @override
  Widget build(BuildContext context) => BlocProvider(
        create: (context) =>
            CultureDetailCubit(CultureDatabase())..loadCultureDetail(cultureId),
        child: _CultureDetailView(cultureId: cultureId),
      );
}

class _CultureDetailView extends StatelessWidget {
  const _CultureDetailView({required this.cultureId});

  final String cultureId;

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(
          title: const Text('Culture Details'),
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        ),
        body: BlocBuilder<CultureDetailCubit, CultureDetailState>(
          builder: (context, state) => state.when(
            initial: () => const Center(child: CircularProgressIndicator()),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: (culture, subcultures) => _CultureDetailContent(
              culture: culture,
              subcultures: subcultures,
            ),
            error: (message) => _ErrorWidget(message: message),
          ),
        ),
      );
}

class _CultureDetailContent extends StatelessWidget {
  const _CultureDetailContent({
    required this.culture,
    required this.subcultures,
  });

  final Culture culture;
  final List<Subculture> subcultures;

  @override
  Widget build(BuildContext context) => SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _CultureInfoCard(culture: culture),
            const SizedBox(height: 16),
            _CreateSubcultureButton(culture: culture),
            const SizedBox(height: 24),
            _SubculturesSection(subcultures: subcultures),
          ],
        ),
      );
}

class _CultureInfoCard extends StatelessWidget {
  const _CultureInfoCard({required this.culture});

  final Culture culture;

  @override
  Widget build(BuildContext context) => Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      culture.cultureId,
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                  ),
                  _StatusIndicator(status: culture.status),
                ],
              ),
              const SizedBox(height: 16),
              _InfoRow(
                label: 'Species',
                value: culture.species,
              ),
              _InfoRow(
                label: 'Explant Type',
                value: culture.explantType,
              ),
              if (culture.sourcePlantId != null)
                _InfoRow(
                  label: 'Source Plant',
                  value: culture.sourcePlantId!,
                ),
              _InfoRow(
                label: 'Initiation Date',
                value: _formatDate(culture.initiationDate),
              ),
              _InfoRow(
                label: 'Medium Composition',
                value: culture.mediumComposition,
              ),
              if (culture.initialConditions != null)
                _InfoRow(
                  label: 'Initial Conditions',
                  value: culture.initialConditions!,
                ),
              _InfoRow(
                label: 'Days Since Initiation',
                value: '${_daysSinceInitiation(culture.initiationDate)} days',
              ),
            ],
          ),
        ),
      );
}

class _InfoRow extends StatelessWidget {
  const _InfoRow({
    required this.label,
    required this.value,
  });

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) => Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 120,
              child: Text(
                '$label:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      );
}

class _CreateSubcultureButton extends StatelessWidget {
  const _CreateSubcultureButton({required this.culture});

  final Culture culture;

  @override
  Widget build(BuildContext context) => SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () => _navigateToCreateSubculture(context, culture),
          icon: const Icon(Icons.add),
          label: const Text('Create Subculture'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      );

  void _navigateToCreateSubculture(BuildContext context, Culture culture) {
    Navigator.of(context).push(
      MaterialPageRoute<void>(
        builder: (context) => CreateSubcultureScreen(parentCulture: culture),
      ),
    );
  }
}

class _SubculturesSection extends StatelessWidget {
  const _SubculturesSection({required this.subcultures});

  final List<Subculture> subcultures;

  @override
  Widget build(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Subcultures (${subcultures.length})',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 12),
          if (subcultures.isEmpty)
            _EmptySubculturesWidget()
          else
            ...subcultures.map((subculture) => _SubcultureCard(
                  subculture: subculture,
                )),
        ],
      );
}

class _EmptySubculturesWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              Icon(
                Icons.science_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'No Subcultures Yet',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first subculture using the button above.',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
}

class _SubcultureCard extends StatelessWidget {
  const _SubcultureCard({required this.subculture});

  final Subculture subculture;

  @override
  Widget build(BuildContext context) => Card(
        margin: const EdgeInsets.only(bottom: 8.0),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Icon(
                Icons.account_tree,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subculture.subcultureId,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Created: ${_formatDate(subculture.subcultureDate)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      '${subculture.explantCount} explants',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              _StatusIndicator(status: subculture.status),
            ],
          ),
        ),
      );
}

class _StatusIndicator extends StatelessWidget {
  const _StatusIndicator({required this.status});

  final CultureStatus status;

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getStatusColor(context),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status.displayName,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: _getTextColor(context),
                fontWeight: FontWeight.w500,
              ),
        ),
      );

  Color _getStatusColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (status) {
      case CultureStatus.healthy:
        return colorScheme.primaryContainer;
      case CultureStatus.contaminated:
        return colorScheme.errorContainer;
      case CultureStatus.readyForTransfer:
      case CultureStatus.inRooting:
      case CultureStatus.acclimatizing:
        return colorScheme.tertiaryContainer;
      case CultureStatus.completed:
      case CultureStatus.disposed:
        return colorScheme.surfaceContainerHighest;
    }
  }

  Color _getTextColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (status) {
      case CultureStatus.healthy:
        return colorScheme.onPrimaryContainer;
      case CultureStatus.contaminated:
        return colorScheme.onErrorContainer;
      case CultureStatus.readyForTransfer:
      case CultureStatus.inRooting:
      case CultureStatus.acclimatizing:
        return colorScheme.onTertiaryContainer;
      case CultureStatus.completed:
      case CultureStatus.disposed:
        return colorScheme.onSurfaceVariant;
    }
  }
}

class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.message});

  final String message;

  @override
  Widget build(BuildContext context) => Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SelectableText.rich(
            TextSpan(
              text: 'Error: $message',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
}

String _formatDate(DateTime date) => '${date.day}/${date.month}/${date.year}';

int _daysSinceInitiation(DateTime initiationDate) =>
    DateTime.now().difference(initiationDate).inDays;
