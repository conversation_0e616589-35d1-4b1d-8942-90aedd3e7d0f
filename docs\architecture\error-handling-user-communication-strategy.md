# Error Handling & User Communication Strategy

### Error Classification Framework

CultureStack's error handling system categorizes errors by severity, user impact, and recovery strategy to provide appropriate user experiences.

#### Error Severity Levels

```dart
@JsonEnum()
enum ErrorSeverity {
  @JsonValue('INFO') info,           // Informational, no action required
  @JsonValue('WARNING') warning,     // Caution needed, can continue
  @JsonValue('ERROR') error,         // Action failed, user intervention needed
  @JsonValue('CRITICAL') critical,   // System issue, contact support
}

@JsonEnum()
enum ErrorCategory {
  @JsonValue('NETWORK') network,           // Connectivity issues
  @JsonValue('SYNC') sync,                 // Data synchronization problems
  @JsonValue('VALIDATION') validation,     // User input validation
  @JsonValue('PERMISSION') permission,     // Access/authorization issues
  @JsonValue('STORAGE') storage,           // Local/cloud storage problems
  @JsonValue('PLATFORM') platform,        // Platform-specific issues
}

@JsonEnum()
enum RecoveryStrategy {
  @JsonValue('RETRY') retry,               // User can retry operation
  @JsonValue('ALTERNATIVE') alternative,   // Suggest alternative approach
  @JsonValue('MANUAL') manual,             // Requires manual intervention
  @JsonValue('SUPPORT') support,           // Contact support needed
}
```

### User-Facing Error Messages Style Guide

#### Message Structure Template
```
[CONTEXT] [WHAT HAPPENED] [WHY IT MATTERS] [WHAT TO DO]
```

#### Error Message Guidelines

**1. Clear and Specific**
- ❌ "Something went wrong"
- ✅ "Unable to save your culture observation due to low storage space"

**2. User-Centered Language**
- ❌ "API rate limit exceeded"
- ✅ "Too many sync requests. We'll automatically retry in 2 minutes"

**3. Actionable Guidance**
- ❌ "Network error occurred"
- ✅ "No internet connection. Your changes are saved locally and will sync when online"

**4. Reassuring Tone**
- ❌ "Data upload failed"
- ✅ "Your photos are safely stored on your device. We'll upload them when connectivity improves"

### Error Message Templates by Category

#### Network & Connectivity Errors
```dart
class NetworkErrorMessages {
  static const String noConnection =
    "No internet connection detected. Your work is saved locally and will sync automatically when you're back online.";

  static const String slowConnection =
    "Slow connection detected. Large photos may take longer to sync. Continue working - we'll handle the uploads in the background.";

  static const String connectionTimeout =
    "Connection timed out while syncing. Don't worry - your data is safe locally. We'll retry automatically in a few minutes.";

  static const String serverUnavailable =
    "Our sync service is temporarily unavailable. Your data remains safe on your device and will sync once service resumes.";
}
```

#### Google Services Errors
```dart
class GoogleServicesErrorMessages {
  static const String signInFailed =
    "Unable to sign in to Google. You can continue using CultureStack in offline mode, or try signing in again later.";

  static const String driveQuotaExceeded =
    "Your Google Drive storage is full. Free up space or upgrade your storage to continue syncing photos and data.";

  static const String rateLimitExceeded =
    "We're syncing your data more slowly to respect Google's limits. Your information is safe and will continue uploading.";

  static const String servicesUnavailable =
    "Google services aren't available in your region. CultureStack works fully offline - your data stays on your device.";

  static String drivePermissionDenied =
    "CultureStack needs permission to access your Google Drive for syncing. Grant permission in Settings to enable cloud backup.";
}
```

#### Data & Validation Errors
```dart
class DataErrorMessages {
  static const String cultureNameRequired =
    "Please enter a name for your culture. This helps you identify it in your timeline.";

  static const String invalidDate =
    "The date you entered seems incorrect. Please check and enter a valid date.";

  static const String photoTooLarge =
    "This photo is quite large. We'll compress it to save space while keeping good quality.";

  static const String duplicateCultureId =
    "This culture ID already exists. We've suggested a new unique ID, or you can enter your own.";

  static String cultureLimit(int current, int max) =>
    "You've reached your limit of $max cultures. Consider upgrading to Pro for unlimited cultures, or mark some as completed.";
}
```

#### Sync & Conflict Errors
```dart
class SyncErrorMessages {
  static const String conflictDetected =
    "This culture was modified on another device. Choose which version to keep, or we can merge the changes for you.";

  static const String syncStalled =
    "Sync is taking longer than usual. Your data is safe locally. Check your internet connection or try again later.";

  static const String partialSyncComplete =
    "Most of your data synced successfully. A few items are still uploading in the background.";

  static String oldDataFound(int daysOld) =>
    "Found culture data from $daysOld days ago that hasn't synced. Would you like to upload it now?";
}
```

### Error Recovery Workflow Patterns

#### Recovery Workflow Framework
```dart
abstract class ErrorRecoveryWorkflow {
  Future<RecoveryResult> attemptRecovery(CultureStackError error);
  List<RecoveryAction> getAvailableActions(CultureStackError error);
  Future<void> executeRecoveryAction(RecoveryAction action);
}

@freezed
class RecoveryAction with _$RecoveryAction {
  const factory RecoveryAction({
    required String id,
    required String title,
    required String description,
    required IconData icon,
    required RecoveryType type,
    required bool isPrimary,
  }) = _RecoveryAction;
}

@JsonEnum()
enum RecoveryType {
  @JsonValue('RETRY') retry,
  @JsonValue('SETTINGS') settings,
  @JsonValue('ALTERNATIVE') alternative,
  @JsonValue('HELP') help,
  @JsonValue('CONTACT') contact,
}
```

#### Network Error Recovery
```dart
class NetworkErrorRecovery implements ErrorRecoveryWorkflow {
  @override
  List<RecoveryAction> getAvailableActions(CultureStackError error) {
    switch (error) {
      case NetworkUnavailable():
        return [
          RecoveryAction(
            id: 'check_connection',
            title: 'Check Connection',
            description: 'Test your internet connection',
            icon: Icons.wifi,
            type: RecoveryType.retry,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'work_offline',
            title: 'Continue Offline',
            description: 'Keep working - data will sync later',
            icon: Icons.offline_bolt,
            type: RecoveryType.alternative,
            isPrimary: false,
          ),
          RecoveryAction(
            id: 'network_settings',
            title: 'Network Settings',
            description: 'Open device network settings',
            icon: Icons.settings,
            type: RecoveryType.settings,
            isPrimary: false,
          ),
        ];
      // Additional network error cases...
    }
  }

  @override
  Future<RecoveryResult> attemptRecovery(CultureStackError error) async {
    // Implement automatic recovery strategies
    switch (error) {
      case NetworkUnavailable():
        // Wait and retry with exponential backoff
        await Future.delayed(Duration(seconds: 5));
        final isOnline = await _connectivityService.checkConnection();
        if (isOnline) {
          return RecoveryResult.success('Connection restored');
        }
        return RecoveryResult.failed('Still offline');

      default:
        return RecoveryResult.notApplicable();
    }
  }
}
```

#### Google Services Error Recovery
```dart
class GoogleServicesErrorRecovery implements ErrorRecoveryWorkflow {
  @override
  List<RecoveryAction> getAvailableActions(CultureStackError error) {
    switch (error) {
      case GoogleServicesUnavailable():
        return [
          RecoveryAction(
            id: 'enable_offline_mode',
            title: 'Use Offline Mode',
            description: 'CultureStack works fully without Google services',
            icon: Icons.cloud_off,
            type: RecoveryType.alternative,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'check_region',
            title: 'Check Availability',
            description: 'See if Google services work in your area',
            icon: Icons.public,
            type: RecoveryType.help,
            isPrimary: false,
          ),
        ];

      case StorageQuotaExceeded():
        return [
          RecoveryAction(
            id: 'manage_storage',
            title: 'Manage Google Drive',
            description: 'Free up space or upgrade storage',
            icon: Icons.storage,
            type: RecoveryType.settings,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'export_local',
            title: 'Export Locally',
            description: 'Save your data to device storage',
            icon: Icons.download,
            type: RecoveryType.alternative,
            isPrimary: false,
          ),
        ];
    }
  }
}
```

### Error UI Components

#### Error Display Component
```dart
class ErrorDisplayWidget extends StatelessWidget {
  final CultureStackError error;
  final List<RecoveryAction> recoveryActions;
  final VoidCallback? onDismiss;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    required this.recoveryActions,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: _getErrorColor(error.severity),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getErrorIcon(error.category)),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getErrorTitle(error),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: onDismiss,
                  ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              _getErrorMessage(error),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (recoveryActions.isNotEmpty) ...[
              SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: recoveryActions.map((action) {
                  return ActionChip(
                    avatar: Icon(action.icon, size: 18),
                    label: Text(action.title),
                    onPressed: () => _executeAction(action),
                    backgroundColor: action.isPrimary
                        ? Theme.of(context).primaryColor
                        : null,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Colors.blue.shade50;
      case ErrorSeverity.warning:
        return Colors.orange.shade50;
      case ErrorSeverity.error:
        return Colors.red.shade50;
      case ErrorSeverity.critical:
        return Colors.red.shade100;
    }
  }

  IconData _getErrorIcon(ErrorCategory category) {
    switch (category) {
      case ErrorCategory.network:
        return Icons.wifi_off;
      case ErrorCategory.sync:
        return Icons.sync_problem;
      case ErrorCategory.validation:
        return Icons.warning;
      case ErrorCategory.permission:
        return Icons.lock;
      case ErrorCategory.storage:
        return Icons.storage;
      case ErrorCategory.platform:
        return Icons.error;
    }
  }
}
```

### Error Analytics and Monitoring

#### Error Tracking System
```dart
class ErrorTrackingService {
  final FirebaseAnalytics _analytics;
  final Map<String, int> _errorCounts = {};

  void trackError(CultureStackError error, {
    String? context,
    Map<String, dynamic>? metadata,
  }) {
    // Track error frequency
    final errorKey = '${error.category}_${error.runtimeType}';
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;

    // Log to analytics (without PII)
    _analytics.logEvent(
      name: 'error_occurred',
      parameters: {
        'error_category': error.category.name,
        'error_severity': error.severity.name,
        'error_type': error.runtimeType.toString(),
        'context': context ?? 'unknown',
        'occurrence_count': _errorCounts[errorKey],
        'user_type': _getUserType(),
        'app_version': _getAppVersion(),
        ...?metadata,
      },
    );
  }

  void trackRecoveryAttempt(RecoveryAction action, bool successful) {
    _analytics.logEvent(
      name: 'error_recovery_attempt',
      parameters: {
        'recovery_action': action.id,
        'recovery_type': action.type.name,
        'successful': successful,
      },
    );
  }

  Future<ErrorInsights> generateErrorInsights() async {
    return ErrorInsights(
      topErrors: _getTopErrors(),
      errorTrends: await _getErrorTrends(),
      recoverySuccessRates: await _getRecoverySuccessRates(),
    );
  }
}
```

### Error Prevention Strategies

#### Proactive Error Prevention
```dart
class ErrorPreventionService {
  final ConnectivityService _connectivity;
  final StorageService _storage;
  final QuotaTracker _quotaTracker;

  Future<PreventionResult> performPreventiveChecks() async {
    final issues = <PreventiveIssue>[];

    // Check storage space
    final storageInfo = await _storage.getStorageInfo();
    if (storageInfo.freeSpacePercent < 10) {
      issues.add(PreventiveIssue(
        type: IssueType.lowStorage,
        severity: IssueSeverity.warning,
        message: "Device storage is running low. Consider freeing up space to prevent save failures.",
        suggestedActions: [
          "Delete old photos or files",
          "Move photos to cloud storage",
          "Clear app cache",
        ],
      ));
    }

    // Check quota usage
    final quotaStatus = _quotaTracker.getCurrentStatus();
    if (quotaStatus.requestsUsed > 800) { // 80% of limit
      issues.add(PreventiveIssue(
        type: IssueType.approachingQuota,
        severity: IssueSeverity.info,
        message: "Approaching sync limits. We'll slow down uploads to prevent errors.",
        suggestedActions: [
          "Sync will continue more slowly",
          "Consider upgrading Google Drive storage",
        ],
      ));
    }

    // Check connectivity quality
    final connectivityQuality = await _connectivity.getConnectionQuality();
    if (connectivityQuality == ConnectionQuality.poor) {
      issues.add(PreventiveIssue(
        type: IssueType.poorConnection,
        severity: IssueSeverity.info,
        message: "Weak internet signal detected. Large uploads may be delayed.",
        suggestedActions: [
          "Connect to stronger WiFi if available",
          "Large photos will sync when signal improves",
        ],
      ));
    }

    return PreventionResult(issues: issues);
  }
}
```

This comprehensive error handling strategy ensures users receive clear, actionable guidance while maintaining confidence in data safety and app reliability.

