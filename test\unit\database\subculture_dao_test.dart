import 'package:flutter_test/flutter_test.dart';
import 'package:drift/native.dart';
import 'package:drift/drift.dart';

import '../../../lib/data/database/database.dart';
import '../../../lib/data/models/culture_status.dart';

void main() {
  group('Subculture DAO Tests', () {
    late CultureDatabase database;

    setUp(() async {
      database = CultureDatabase.forTesting(NativeDatabase.memory());
    });

    tearDown(() async {
      await database.close();
    });

    test('insertSubculture creates new subculture', () async {
      // Arrange
      final parentCultureId = 'parent-culture-id';
      final parentCulture = CulturesCompanion.insert(
        id: parentCultureId,
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertCulture(parentCulture);

      final subcultureId = 'subculture-id';
      final subculture = SubculturesCompanion.insert(
        id: subcultureId,
        subcultureId: 'S001',
        parentCultureId: parentCultureId,
        subcultureDate: DateTime(2025, 1, 15),
        mediumComposition: 'MS Medium',
        explantCount: 5,
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Act
      await database.insertSubculture(subculture);

      // Assert
      final allSubcultures = await database.getAllSubcultures();
      final retrievedSubculture =
          allSubcultures.firstWhere((s) => s.id == subcultureId);
      expect(retrievedSubculture.subcultureId, equals('S001'));
      expect(retrievedSubculture.parentCultureId, equals(parentCultureId));
      expect(retrievedSubculture.explantCount, equals(5));
    });

    test('getSubculturesForCulture returns correct subcultures', () async {
      // Arrange
      final parentCultureId = 'parent-culture-id';
      final parentCulture = CulturesCompanion.insert(
        id: parentCultureId,
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertCulture(parentCulture);

      // Create multiple subcultures
      final subculture1 = SubculturesCompanion.insert(
        id: 'subculture-1',
        subcultureId: 'S001',
        parentCultureId: parentCultureId,
        subcultureDate: DateTime(2025, 1, 15),
        mediumComposition: 'MS Medium',
        explantCount: 5,
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      final subculture2 = SubculturesCompanion.insert(
        id: 'subculture-2',
        subcultureId: 'S002',
        parentCultureId: parentCultureId,
        subcultureDate: DateTime(2025, 1, 20),
        mediumComposition: 'MS Medium',
        explantCount: 3,
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await database.insertSubculture(subculture1);
      await database.insertSubculture(subculture2);

      // Act
      final subcultures =
          await database.getSubculturesForCulture(parentCultureId);

      // Assert
      expect(subcultures.length, equals(2));
      expect(subcultures.map((s) => s.subcultureId),
          containsAll(['S001', 'S002']));
    });

    test('generateSubcultureId creates sequential IDs', () async {
      // Arrange
      final parentCultureId = 'parent-culture-id';
      final parentCulture = CulturesCompanion.insert(
        id: parentCultureId,
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertCulture(parentCulture);

      // Act
      final firstId = await database.generateSubcultureId(parentCultureId);

      // Create a subculture with the first ID
      final subculture1 = SubculturesCompanion.insert(
        id: 'subculture-1',
        subcultureId: firstId,
        parentCultureId: parentCultureId,
        subcultureDate: DateTime(2025, 1, 15),
        mediumComposition: 'MS Medium',
        explantCount: 5,
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertSubculture(subculture1);

      final secondId = await database.generateSubcultureId(parentCultureId);

      // Assert
      expect(firstId, equals('S001'));
      expect(secondId, equals('S002'));
    });

    test('soft delete excludes deleted subcultures', () async {
      // Arrange
      final parentCultureId = 'parent-culture-id';
      final parentCulture = CulturesCompanion.insert(
        id: parentCultureId,
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertCulture(parentCulture);

      final subculture = SubculturesCompanion.insert(
        id: 'subculture-id',
        subcultureId: 'S001',
        parentCultureId: parentCultureId,
        subcultureDate: DateTime(2025, 1, 15),
        mediumComposition: 'MS Medium',
        explantCount: 5,
        status: CultureStatus.healthy,
        isDeleted: const Value(true), // Soft deleted
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await database.insertSubculture(subculture);

      // Act
      final subcultures =
          await database.getSubculturesForCulture(parentCultureId);

      // Assert
      expect(subcultures.length, equals(0));
    });
  });
}
