import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import '../models/culture_status.dart';

part 'database.g.dart';

/// Cultures table definition
class Cultures extends Table {
  TextColumn get id => text()();
  TextColumn get cultureId => text()();
  TextColumn get species => text()();
  TextColumn get explantType => text()();
  TextColumn get sourcePlantId => text().nullable()();
  DateTimeColumn get initiationDate => dateTime()();
  TextColumn get mediumComposition => text()();
  TextColumn get recipeId => text().nullable()();
  TextColumn get initialConditions => text().nullable()();
  IntColumn get status => integer().map(const CultureStatusConverter())();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  DateTimeColumn get syncedAt => dateTime().nullable()();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}

/// Subcultures table definition
class Subcultures extends Table {
  TextColumn get id => text()();
  TextColumn get subcultureId => text()();
  TextColumn get parentCultureId => text()();
  DateTimeColumn get subcultureDate => dateTime()();
  TextColumn get mediumComposition => text()();
  IntColumn get explantCount => integer()();
  IntColumn get status => integer().map(const CultureStatusConverter())();
  TextColumn get notes => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  DateTimeColumn get syncedAt => dateTime().nullable()();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}

/// Type converter for CultureStatus enum
class CultureStatusConverter extends TypeConverter<CultureStatus, int> {
  const CultureStatusConverter();

  @override
  CultureStatus fromSql(int fromDb) {
    return CultureStatus.fromValue(fromDb);
  }

  @override
  int toSql(CultureStatus value) {
    return value.value;
  }
}

/// Main database class
@DriftDatabase(tables: [Cultures, Subcultures])
class CultureDatabase extends _$CultureDatabase {
  CultureDatabase() : super(_openConnection());

  CultureDatabase.forTesting(QueryExecutor executor) : super(executor);

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
    );
  }

  // Culture operations
  Future<List<Culture>> getAllCultures() => select(cultures).get();

  Stream<List<Culture>> watchAllCultures() => select(cultures).watch();

  Future<Culture?> getCultureById(String id) =>
    (select(cultures)..where((c) => c.id.equals(id))).getSingleOrNull();

  Future<int> insertCulture(CulturesCompanion culture) => 
    into(cultures).insert(culture);

  Future<bool> updateCulture(CulturesCompanion culture) => 
    update(cultures).replace(culture);

  Future<int> deleteCulture(String id) => 
    (delete(cultures)..where((c) => c.id.equals(id))).go();

  // Subculture operations
  Future<List<Subculture>> getAllSubcultures() => select(subcultures).get();
  
  Future<List<Subculture>> getSubculturesForCulture(String cultureId) => 
    (select(subcultures)..where((s) => s.parentCultureId.equals(cultureId))).get();

  Future<int> insertSubculture(SubculturesCompanion subculture) => 
    into(subcultures).insert(subculture);

  Future<bool> updateSubculture(SubculturesCompanion subculture) => 
    update(subcultures).replace(subculture);

  Future<int> deleteSubculture(String id) => 
    (delete(subcultures)..where((s) => s.id.equals(id))).go();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'culture_stack.db'));
    return NativeDatabase(file);
  });
}
