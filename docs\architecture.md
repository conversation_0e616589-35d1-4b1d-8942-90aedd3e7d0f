# CultureStack Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for **CultureStack**, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

Based on review of the PRD and project documentation, **CultureStack** is specified as a **Flutter mobile application** built from scratch.

**Analysis:**
1. **No starter templates mentioned** in PRD or documentation
2. **Greenfield project** - This is a new Flutter cross-platform application
3. **Technology constraints identified:**
   - Flutter development using Dart language
   - Local SQLite database with sqflite/drift
   - Google Services integration (Sign-In, Drive, Play Billing)
   - Offline-first architecture with cloud sync
   - Cross-platform deployment (Android primary, iOS future)

**Architectural Decision:**
Since this is a **Flutter mobile application** rather than a traditional web fullstack application, I recommend adapting this architecture document to focus on:
- **Client Architecture:** Flutter app structure with Bloc pattern
- **Cloud Integration:** Google Drive API, Google Services
- **Local Data Layer:** SQLite with Drift ORM
- **Sync Architecture:** Offline-first with cloud backup
- **State Management:** Flutter Bloc for predictable state management

**Template Adaptation Required:** This fullstack template will be adapted for Flutter mobile-first architecture with cloud integration rather than traditional web frontend/backend separation.

**Status:** N/A - Greenfield native Android project

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-25 | 1.0 | Initial architecture document creation | Winston (Architect) |

## High Level Architecture

### Technical Summary
CultureStack employs a **Flutter cross-platform architecture** with offline-first design, utilizing local SQLite storage synchronized with Google Drive for cloud backup. The application integrates Google Services (Authentication, Drive API, Play Billing) through platform channels to provide seamless user experience while maintaining offline functionality. The architecture prioritizes data integrity and user experience in laboratory environments, supporting the PRD's goal of reducing culture failure rates through reliable mobile access to tracking capabilities across Android and iOS platforms.

### Platform and Infrastructure Choice

**Analysis of Platform Options:**

1. **Google Cloud + Flutter (Recommended)**
   - **Pros:** Cross-platform development, Google Services integration via platform channels, seamless Play Store billing, Drive API optimization, single codebase for Android/iOS
   - **Cons:** Platform channel complexity for native features, Google Services dependency
   - **Rationale:** Aligns perfectly with PRD requirements for Google Drive sync and Play Billing while enabling future iOS support

2. **AWS + Flutter**
   - **Pros:** Enterprise scalability, diverse service ecosystem, cross-platform development
   - **Cons:** Additional complexity for Drive integration, separate billing system needed
   - **Rationale:** Overengineered for mobile-first application

3. **Firebase + Flutter**
   - **Pros:** Real-time sync, integrated authentication, mobile-optimized, excellent Flutter integration
   - **Cons:** Conflicts with Google Drive requirement, different data model
   - **Rationale:** Would require PRD changes for sync mechanism

**Recommendation:** **Google Cloud Platform + Flutter**

**Platform:** Google Cloud Platform
**Key Services:** Google Drive API v3, Google Sign-In API, Google Play Billing Library, Flutter Workmanager
**Deployment Host and Regions:** Google Play Store distribution (Android), App Store distribution (iOS future), user data stored in personal Google Drive

### Repository Structure

**Structure:** Monorepo with Flutter-focused organization
**Monorepo Tool:** Flutter package structure with pub dependencies
**Package Organization:** Feature-based packages (culture, recipes, sync, auth) with shared core package following Flutter conventions

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Device"
        A[Flutter App]
        B[SQLite Database]
        C[Local File Storage]
        D[Platform Channels]
        A --> B
        A --> C
        A --> D
    end

    subgraph "Google Services"
        E[Google Drive API]
        F[Google Sign-In]
        G[Play Billing]
    end

    subgraph "User's Google Account"
        H[Personal Google Drive]
        I[Play Store Account]
    end

    D --> E
    D --> F
    D --> G
    E --> H
    F --> I
    G --> I

    J[Push Notifications] --> A
    K[Flutter Workmanager] --> A
```

### Architectural Patterns

- **Offline-First Architecture:** Local SQLite as primary data store with cloud sync - _Rationale:_ Essential for laboratory environments with unreliable connectivity
- **Flutter Bloc Pattern:** Business Logic Components for predictable state management - _Rationale:_ Flutter best practice for scalable, testable applications
- **Repository Pattern:** Abstract data access between local and cloud storage - _Rationale:_ Enables seamless sync and testing isolation
- **Clean Architecture:** Layered dependency structure with domain-driven design - _Rationale:_ Supports complex business logic for culture management
- **Event-Driven Updates:** Stream-based state management for data synchronization - _Rationale:_ Ensures UI consistency during background sync operations using Flutter's reactive paradigm

## Tech Stack

This is the **DEFINITIVE** technology selection for the entire CultureStack project. All development must use these exact versions and technologies.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale & Constraints |
|----------|------------|---------|---------|-------------------------|
| Mobile Platform | Flutter | 3.16+ | Cross-platform mobile framework | PRD requirement + future iOS; **Constraint:** Platform channel complexity |
| Programming Language | Dart | 3.2+ | Primary development language | Flutter-native language; **Constraint:** Requires developer Dart expertise |
| UI Framework | Flutter Widgets | Built-in | Declarative UI framework | Native Flutter approach; **Constraint:** Custom platform styling needed |
| Database | SQLite + Drift | Drift 2.14+ | Local data storage and ORM | Offline-first; **Constraint:** Performance degrades >100K records |
| Database Encryption | SQLCipher | Via drift | Database encryption for sensitive data | **Added:** Security for culture data on compromised devices |
| Authentication | Google Sign-In (Flutter) | google_sign_in 6.1+ | User authentication for premium features | PRD requirement; **Constraint:** Platform channel dependency |
| Fallback Auth | Guest Mode | Custom | Local-only access without Google account | **Added:** Graceful degradation for Google Services limitations |
| Cloud Storage | Google Drive API | googleapis 11.0+ | User data synchronization | PRD requirement; **Constraint:** 15GB storage limit, rate limits |
| Billing | Flutter In-App Purchase | in_app_purchase 3.1+ | Premium subscription management | PRD requirement; **Constraint:** Platform-specific implementations |
| Image Handling | Cached Network Image | cached_network_image 3.3+ | Async image loading and caching | Photo management; **Constraint:** Memory usage for large galleries |
| Image Compression | Flutter Image Compress | flutter_image_compress 2.0+ | Automatic photo compression | **Added:** Mitigate storage constraints |
| Networking | Dio + HTTP | dio 5.3+ | HTTP client for API calls | **Constraint:** Google Drive API rate limiting |
| Serialization | json_annotation | json_annotation 4.8+ | JSON serialization for sync | Type-safe; excellent Drift compatibility |
| Background Tasks | Flutter Workmanager | workmanager 0.5+ | Sync scheduling and notifications | Flutter recommended; handles Google Drive rate limits |
| Dependency Injection | Get It + Injectable | get_it 7.6+ / injectable 2.3+ | Dependency management | Flutter-recommended DI pattern |
| Testing Framework | Flutter Test + Integration Test | Built-in | Comprehensive cross-platform testing | Multi-layer testing with platform channel mocking |
| Build System | Flutter Build | flutter 3.16+ | Build automation | Flutter standard; supports multi-package architecture |
| Version Control | Git | 2.40+ | Source code management | Industry standard |
| State Management | Flutter Bloc | flutter_bloc 8.1+ | Predictable state management | Flutter best practice; **Constraint:** Learning curve |
| Code Generation | Build Runner | build_runner 2.4+ | Code generation for serialization | Dart ecosystem standard |
| Push Notifications | Firebase Cloud Messaging | firebase_messaging 14.7+ | Reminder notifications | **Constraint:** Platform-specific setup |
| Analytics | Firebase Analytics | firebase_analytics 10.7+ | Usage tracking and insights | Privacy-compliant; **Constraint:** Google Services dependency |
| Crash Reporting | Firebase Crashlytics | firebase_crashlytics 3.4+ | Error monitoring | Production stability; **Constraint:** Google Services dependency |

### Additional Architecture Components (Based on Constraint Analysis):

- **Storage Monitoring Service:** Tracks Google Drive usage and warns users before limits
- **Conflict Resolution Engine:** Handles sync conflicts with user-friendly resolution UI
- **Graceful Degradation Manager:** Provides offline-only functionality when Google Services unavailable
- **Data Archiving System:** Manages culture record lifecycle to prevent database performance issues

## Cross-Platform Testing Strategy

### Testing Architecture Overview

CultureStack's testing strategy addresses the unique challenges of cross-platform Flutter development with Google Services integration, offline-first architecture, and multi-device synchronization.

#### Testing Pyramid Structure

```mermaid
graph TB
    A[E2E Tests<br/>Platform-specific integration<br/>5% of test suite] --> B[Integration Tests<br/>Component interaction<br/>25% of test suite]
    B --> C[Unit Tests<br/>Business logic isolation<br/>70% of test suite]

    D[Platform Channel Tests<br/>Google Services mocking] --> B
    E[Database Tests<br/>SQLite operations] --> B
    F[Sync Tests<br/>Offline/online scenarios] --> B
```

### Layer 1: Unit Testing (70% Coverage Target)

#### Business Logic Testing
```dart
// Example: Culture creation business logic testing
@testWidgets('CultureService creates culture with valid data', (tester) async {
  // Arrange
  final mockCultureDao = MockCultureDao();
  final mockEventBus = MockEventBus();
  final cultureService = CultureServiceImpl(mockCultureDao, mockEventBus);

  final request = CreateCultureRequest(
    species: 'Orchid',
    explantType: 'Leaf',
    initiationDate: DateTime.now(),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile conditions',
  );

  when(mockCultureDao.insertCulture(any)).thenAnswer((_) async => 1);

  // Act
  final result = await cultureService.createCulture(request);

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.species, 'Orchid');
  verify(mockCultureDao.insertCulture(any)).called(1);
  verify(mockEventBus.fire(any)).called(1);
});

// Example: Rate limiting logic testing
test('DriveApiRateLimiter respects token bucket limits', () async {
  // Arrange
  final rateLimiter = DriveApiRateLimiter();

  // Act & Assert
  // Should allow initial requests
  expect(await rateLimiter.canProceed(10), true);
  expect(await rateLimiter.canProceed(10), true);

  // Should reject when bucket is empty
  for (int i = 0; i < 100; i++) {
    await rateLimiter.canProceed(10);
  }
  expect(await rateLimiter.canProceed(10), false);
});
```

#### Data Model Testing
```dart
// Freezed data class serialization testing
test('Culture serialization maintains data integrity', () {
  // Arrange
  final originalCulture = Culture(
    id: 'test-id',
    cultureId: 'C001',
    species: 'Orchid',
    explantType: 'Leaf',
    initiationDate: DateTime.parse('2025-01-01'),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile',
    status: CultureStatus.healthy,
  );

  // Act
  final json = originalCulture.toJson();
  final deserializedCulture = Culture.fromJson(json);

  // Assert
  expect(deserializedCulture, equals(originalCulture));
  expect(deserializedCulture.status, CultureStatus.healthy);
});
```

### Layer 2: Integration Testing (25% Coverage Target)

#### Database Integration Testing
```dart
// Database operations with real SQLite
@testWidgets('Culture CRUD operations with database', (tester) async {
  // Arrange
  final database = await $FloorAppDatabase
      .inMemoryDatabaseBuilder()
      .build();
  final cultureDao = database.cultureDao;

  final culture = Culture(
    id: 'test-culture',
    cultureId: 'C001',
    species: 'Test Plant',
    explantType: 'Leaf',
    initiationDate: DateTime.now(),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile',
  );

  // Act & Assert
  await cultureDao.insertCulture(culture.toCompanion());
  final retrieved = await cultureDao.getCultureById('test-culture');
  expect(retrieved?.species, 'Test Plant');

  // Update test
  final updated = culture.copyWith(status: CultureStatus.contaminated);
  await cultureDao.updateCulture(updated);
  final afterUpdate = await cultureDao.getCultureById('test-culture');
  expect(afterUpdate?.status, CultureStatus.contaminated.name);

  // Cleanup
  await database.close();
});
```

#### Platform Channel Integration Testing
```dart
// Google Services platform channel testing with mocking
@testWidgets('Google Sign-In platform channel integration', (tester) async {
  // Arrange
  const MethodChannel channel = MethodChannel('plugins.flutter.io/google_sign_in');
  final List<MethodCall> log = <MethodCall>[];

  // Mock platform channel responses
  tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
    channel,
    (MethodCall methodCall) async {
      log.add(methodCall);
      switch (methodCall.method) {
        case 'signIn':
          return {
            'displayName': 'Test User',
            'email': '<EMAIL>',
            'id': 'test-user-id',
            'photoUrl': null,
          };
        case 'isSignedIn':
          return true;
        default:
          return null;
      }
    },
  );

  final authService = GoogleAuthService(GoogleSignIn());

  // Act
  final result = await authService.signInWithGoogle();

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.email, '<EMAIL>');
  expect(log, hasLength(1));
  expect(log.first.method, 'signIn');
});
```

#### Sync Integration Testing
```dart
// Offline/online sync scenario testing
@testWidgets('Sync queue processes operations when online', (tester) async {
  // Arrange
  final mockDriveService = MockGoogleDriveService();
  final syncQueueDao = MockSyncQueueDao();
  final syncService = SyncServiceImpl(mockDriveService, syncQueueDao);

  final queuedOperations = [
    SyncOperation(
      entityType: 'Culture',
      entityId: 'culture-1',
      operation: SyncOperationType.create,
      priority: SyncPriority.high,
    ),
    SyncOperation(
      entityType: 'Observation',
      entityId: 'obs-1',
      operation: SyncOperationType.update,
      priority: SyncPriority.normal,
    ),
  ];

  when(syncQueueDao.getPendingOperations()).thenAnswer((_) async => queuedOperations);
  when(mockDriveService.syncWithRateLimit(any))
      .thenAnswer((_) async => Result.success(SyncResult(
            totalOperations: 2,
            successfulOperations: 2,
            failedOperations: 0,
            rateLimitHit: false,
          )));

  // Act
  final result = await syncService.processQueueWhenOnline();

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.successfulOperations, 2);
  verify(mockDriveService.syncWithRateLimit(any)).called(1);
  verify(syncQueueDao.markOperationsComplete(any)).called(1);
});
```

### Layer 3: Platform-Specific End-to-End Testing (5% Coverage Target)

#### Critical User Flow Testing
```dart
// Full user journey: Create culture → Add observation → Sync
@testWidgets('Complete culture management flow', (tester) async {
  app.main();
  await tester.pumpAndSettle();

  // Navigate to create culture
  await tester.tap(find.byKey(Key('add_culture_fab')));
  await tester.pumpAndSettle();

  // Fill culture creation form
  await tester.enterText(find.byKey(Key('species_field')), 'Test Orchid');
  await tester.enterText(find.byKey(Key('explant_type_field')), 'Leaf');
  await tester.enterText(find.byKey(Key('medium_composition_field')), 'MS Medium');

  // Submit form
  await tester.tap(find.byKey(Key('create_culture_button')));
  await tester.pumpAndSettle();

  // Verify culture appears in timeline
  expect(find.text('Test Orchid'), findsOneWidget);
  expect(find.byKey(Key('culture_status_healthy')), findsOneWidget);

  // Navigate to culture detail
  await tester.tap(find.text('Test Orchid'));
  await tester.pumpAndSettle();

  // Add observation
  await tester.tap(find.byKey(Key('add_observation_button')));
  await tester.pumpAndSettle();

  await tester.tap(find.byKey(Key('contamination_toggle')));
  await tester.enterText(find.byKey(Key('observation_notes')), 'Some contamination detected');

  await tester.tap(find.byKey(Key('save_observation_button')));
  await tester.pumpAndSettle();

  // Verify status updated
  expect(find.byKey(Key('culture_status_contaminated')), findsOneWidget);
  expect(find.text('Some contamination detected'), findsOneWidget);
});
```

### Platform Channel Testing Strategy

#### Google Services Mocking Framework
```dart
class GoogleServicesMockFramework {
  static void setupMocks() {
    // Google Sign-In mocking
    const MethodChannel signInChannel = MethodChannel('plugins.flutter.io/google_sign_in');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(signInChannel, _handleSignInCalls);

    // Google Drive API mocking
    const MethodChannel driveChannel = MethodChannel('plugins.flutter.io/google_drive');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(driveChannel, _handleDriveCalls);

    // Play Billing mocking
    const MethodChannel billingChannel = MethodChannel('plugins.flutter.io/in_app_purchase');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(billingChannel, _handleBillingCalls);
  }

  static Future<dynamic> _handleSignInCalls(MethodCall call) async {
    switch (call.method) {
      case 'signIn':
        return MockGoogleAccount.standardUser().toMap();
      case 'signOut':
        return null;
      case 'isSignedIn':
        return true;
      default:
        throw MissingPluginException();
    }
  }

  static Future<dynamic> _handleDriveCalls(MethodCall call) async {
    switch (call.method) {
      case 'uploadFile':
        // Simulate successful upload
        await Future.delayed(Duration(milliseconds: 100));
        return {'fileId': 'mock-file-${DateTime.now().millisecondsSinceEpoch}'};
      case 'downloadFile':
        return {'content': 'mock-file-content'};
      case 'listFiles':
        return {'files': []};
      default:
        throw MissingPluginException();
    }
  }
}
```

### Testing Environment Configuration

#### Test Database Setup
```dart
// Isolated test database for each test
class TestDatabaseProvider {
  static Future<AppDatabase> createTestDatabase() async {
    return await $FloorAppDatabase
        .inMemoryDatabaseBuilder()
        .addMigrations([/* test migrations */])
        .build();
  }

  static Future<void> seedTestData(AppDatabase database) async {
    // Insert test recipes
    await database.recipeDao.insertRecipe(
      Recipe(
        id: 'test-recipe-1',
        name: 'Test MS Medium',
        ingredients: [
          Ingredient(name: 'MS Salts', concentration: '4.4', unit: 'g/L'),
          Ingredient(name: 'Sucrose', concentration: '30', unit: 'g/L'),
        ],
        difficultyLevel: DifficultyLevel.beginner,
      ).toCompanion(),
    );

    // Insert test cultures
    await database.cultureDao.insertCulture(
      Culture(
        id: 'test-culture-1',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime.now().subtract(Duration(days: 30)),
        mediumComposition: 'Test MS Medium',
        initialConditions: 'Sterile conditions',
      ).toCompanion(),
    );
  }
}
```

### Performance Testing

#### Load Testing for Database Operations
```dart
test('Database handles large number of cultures efficiently', () async {
  final database = await TestDatabaseProvider.createTestDatabase();
  final stopwatch = Stopwatch()..start();

  // Insert 1000 cultures
  for (int i = 0; i < 1000; i++) {
    await database.cultureDao.insertCulture(
      Culture(
        id: 'culture-$i',
        cultureId: 'C${i.toString().padLeft(3, '0')}',
        species: 'Test Species $i',
        explantType: 'Leaf',
        initiationDate: DateTime.now(),
        mediumComposition: 'MS Medium',
        initialConditions: 'Sterile',
      ).toCompanion(),
    );
  }

  stopwatch.stop();
  expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in <5s

  // Test timeline query performance
  stopwatch.reset();
  stopwatch.start();

  final cultures = await database.cultureDao.getAllActiveCultures();

  stopwatch.stop();
  expect(cultures.length, 1000);
  expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should query in <100ms

  await database.close();
});
```

### Continuous Integration Testing Pipeline

#### GitHub Actions Configuration
```yaml
# .github/workflows/test.yml
name: CultureStack Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - run: flutter pub get
      - run: flutter test --coverage
      - uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

  integration-tests:
    strategy:
      matrix:
        device: [android, ios]
    runs-on: ${{ matrix.device == 'android' && 'ubuntu-latest' || 'macos-latest' }}
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - name: Setup Android SDK (Android only)
        if: matrix.device == 'android'
        uses: android-actions/setup-android@v2
      - name: Setup iOS Simulator (iOS only)
        if: matrix.device == 'ios'
        run: xcrun simctl boot "iPhone 14"
      - run: flutter pub get
      - run: flutter drive --target=test_driver/integration_test.dart
```

### Testing Coverage Goals

| Test Type | Coverage Target | Rationale |
|-----------|----------------|-----------|
| Unit Tests | 85% | Core business logic reliability |
| Integration Tests | 70% | Component interaction validation |
| Platform Channel Tests | 90% | Critical Google Services functionality |
| E2E Tests | 80% of critical paths | User journey reliability |
| Performance Tests | All database operations | Scalability assurance |

### Test Data Management

#### Test Fixtures and Factories
```dart
class CultureTestFactory {
  static Culture createValidCulture({
    String? id,
    String? species,
    CultureStatus? status,
    DateTime? initiationDate,
  }) {
    return Culture(
      id: id ?? 'test-culture-${DateTime.now().millisecondsSinceEpoch}',
      cultureId: 'C001',
      species: species ?? 'Test Orchid',
      explantType: 'Leaf',
      initiationDate: initiationDate ?? DateTime.now(),
      mediumComposition: 'MS Medium',
      initialConditions: 'Sterile conditions',
      status: status ?? CultureStatus.healthy,
    );
  }

  static List<Culture> createCultureBatch(int count) {
    return List.generate(count, (index) => createValidCulture(
      id: 'batch-culture-$index',
      species: 'Batch Species $index',
    ));
  }
}
```

This comprehensive testing strategy ensures reliable cross-platform functionality while addressing the specific challenges of Google Services integration, offline-first architecture, and multi-device synchronization.

## Error Handling & User Communication Strategy

### Error Classification Framework

CultureStack's error handling system categorizes errors by severity, user impact, and recovery strategy to provide appropriate user experiences.

#### Error Severity Levels

```dart
@JsonEnum()
enum ErrorSeverity {
  @JsonValue('INFO') info,           // Informational, no action required
  @JsonValue('WARNING') warning,     // Caution needed, can continue
  @JsonValue('ERROR') error,         // Action failed, user intervention needed
  @JsonValue('CRITICAL') critical,   // System issue, contact support
}

@JsonEnum()
enum ErrorCategory {
  @JsonValue('NETWORK') network,           // Connectivity issues
  @JsonValue('SYNC') sync,                 // Data synchronization problems
  @JsonValue('VALIDATION') validation,     // User input validation
  @JsonValue('PERMISSION') permission,     // Access/authorization issues
  @JsonValue('STORAGE') storage,           // Local/cloud storage problems
  @JsonValue('PLATFORM') platform,        // Platform-specific issues
}

@JsonEnum()
enum RecoveryStrategy {
  @JsonValue('RETRY') retry,               // User can retry operation
  @JsonValue('ALTERNATIVE') alternative,   // Suggest alternative approach
  @JsonValue('MANUAL') manual,             // Requires manual intervention
  @JsonValue('SUPPORT') support,           // Contact support needed
}
```

### User-Facing Error Messages Style Guide

#### Message Structure Template
```
[CONTEXT] [WHAT HAPPENED] [WHY IT MATTERS] [WHAT TO DO]
```

#### Error Message Guidelines

**1. Clear and Specific**
- ❌ "Something went wrong"
- ✅ "Unable to save your culture observation due to low storage space"

**2. User-Centered Language**
- ❌ "API rate limit exceeded"
- ✅ "Too many sync requests. We'll automatically retry in 2 minutes"

**3. Actionable Guidance**
- ❌ "Network error occurred"
- ✅ "No internet connection. Your changes are saved locally and will sync when online"

**4. Reassuring Tone**
- ❌ "Data upload failed"
- ✅ "Your photos are safely stored on your device. We'll upload them when connectivity improves"

### Error Message Templates by Category

#### Network & Connectivity Errors
```dart
class NetworkErrorMessages {
  static const String noConnection =
    "No internet connection detected. Your work is saved locally and will sync automatically when you're back online.";

  static const String slowConnection =
    "Slow connection detected. Large photos may take longer to sync. Continue working - we'll handle the uploads in the background.";

  static const String connectionTimeout =
    "Connection timed out while syncing. Don't worry - your data is safe locally. We'll retry automatically in a few minutes.";

  static const String serverUnavailable =
    "Our sync service is temporarily unavailable. Your data remains safe on your device and will sync once service resumes.";
}
```

#### Google Services Errors
```dart
class GoogleServicesErrorMessages {
  static const String signInFailed =
    "Unable to sign in to Google. You can continue using CultureStack in offline mode, or try signing in again later.";

  static const String driveQuotaExceeded =
    "Your Google Drive storage is full. Free up space or upgrade your storage to continue syncing photos and data.";

  static const String rateLimitExceeded =
    "We're syncing your data more slowly to respect Google's limits. Your information is safe and will continue uploading.";

  static const String servicesUnavailable =
    "Google services aren't available in your region. CultureStack works fully offline - your data stays on your device.";

  static String drivePermissionDenied =
    "CultureStack needs permission to access your Google Drive for syncing. Grant permission in Settings to enable cloud backup.";
}
```

#### Data & Validation Errors
```dart
class DataErrorMessages {
  static const String cultureNameRequired =
    "Please enter a name for your culture. This helps you identify it in your timeline.";

  static const String invalidDate =
    "The date you entered seems incorrect. Please check and enter a valid date.";

  static const String photoTooLarge =
    "This photo is quite large. We'll compress it to save space while keeping good quality.";

  static const String duplicateCultureId =
    "This culture ID already exists. We've suggested a new unique ID, or you can enter your own.";

  static String cultureLimit(int current, int max) =>
    "You've reached your limit of $max cultures. Consider upgrading to Pro for unlimited cultures, or mark some as completed.";
}
```

#### Sync & Conflict Errors
```dart
class SyncErrorMessages {
  static const String conflictDetected =
    "This culture was modified on another device. Choose which version to keep, or we can merge the changes for you.";

  static const String syncStalled =
    "Sync is taking longer than usual. Your data is safe locally. Check your internet connection or try again later.";

  static const String partialSyncComplete =
    "Most of your data synced successfully. A few items are still uploading in the background.";

  static String oldDataFound(int daysOld) =>
    "Found culture data from $daysOld days ago that hasn't synced. Would you like to upload it now?";
}
```

### Error Recovery Workflow Patterns

#### Recovery Workflow Framework
```dart
abstract class ErrorRecoveryWorkflow {
  Future<RecoveryResult> attemptRecovery(CultureStackError error);
  List<RecoveryAction> getAvailableActions(CultureStackError error);
  Future<void> executeRecoveryAction(RecoveryAction action);
}

@freezed
class RecoveryAction with _$RecoveryAction {
  const factory RecoveryAction({
    required String id,
    required String title,
    required String description,
    required IconData icon,
    required RecoveryType type,
    required bool isPrimary,
  }) = _RecoveryAction;
}

@JsonEnum()
enum RecoveryType {
  @JsonValue('RETRY') retry,
  @JsonValue('SETTINGS') settings,
  @JsonValue('ALTERNATIVE') alternative,
  @JsonValue('HELP') help,
  @JsonValue('CONTACT') contact,
}
```

#### Network Error Recovery
```dart
class NetworkErrorRecovery implements ErrorRecoveryWorkflow {
  @override
  List<RecoveryAction> getAvailableActions(CultureStackError error) {
    switch (error) {
      case NetworkUnavailable():
        return [
          RecoveryAction(
            id: 'check_connection',
            title: 'Check Connection',
            description: 'Test your internet connection',
            icon: Icons.wifi,
            type: RecoveryType.retry,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'work_offline',
            title: 'Continue Offline',
            description: 'Keep working - data will sync later',
            icon: Icons.offline_bolt,
            type: RecoveryType.alternative,
            isPrimary: false,
          ),
          RecoveryAction(
            id: 'network_settings',
            title: 'Network Settings',
            description: 'Open device network settings',
            icon: Icons.settings,
            type: RecoveryType.settings,
            isPrimary: false,
          ),
        ];
      // Additional network error cases...
    }
  }

  @override
  Future<RecoveryResult> attemptRecovery(CultureStackError error) async {
    // Implement automatic recovery strategies
    switch (error) {
      case NetworkUnavailable():
        // Wait and retry with exponential backoff
        await Future.delayed(Duration(seconds: 5));
        final isOnline = await _connectivityService.checkConnection();
        if (isOnline) {
          return RecoveryResult.success('Connection restored');
        }
        return RecoveryResult.failed('Still offline');

      default:
        return RecoveryResult.notApplicable();
    }
  }
}
```

#### Google Services Error Recovery
```dart
class GoogleServicesErrorRecovery implements ErrorRecoveryWorkflow {
  @override
  List<RecoveryAction> getAvailableActions(CultureStackError error) {
    switch (error) {
      case GoogleServicesUnavailable():
        return [
          RecoveryAction(
            id: 'enable_offline_mode',
            title: 'Use Offline Mode',
            description: 'CultureStack works fully without Google services',
            icon: Icons.cloud_off,
            type: RecoveryType.alternative,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'check_region',
            title: 'Check Availability',
            description: 'See if Google services work in your area',
            icon: Icons.public,
            type: RecoveryType.help,
            isPrimary: false,
          ),
        ];

      case StorageQuotaExceeded():
        return [
          RecoveryAction(
            id: 'manage_storage',
            title: 'Manage Google Drive',
            description: 'Free up space or upgrade storage',
            icon: Icons.storage,
            type: RecoveryType.settings,
            isPrimary: true,
          ),
          RecoveryAction(
            id: 'export_local',
            title: 'Export Locally',
            description: 'Save your data to device storage',
            icon: Icons.download,
            type: RecoveryType.alternative,
            isPrimary: false,
          ),
        ];
    }
  }
}
```

### Error UI Components

#### Error Display Component
```dart
class ErrorDisplayWidget extends StatelessWidget {
  final CultureStackError error;
  final List<RecoveryAction> recoveryActions;
  final VoidCallback? onDismiss;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    required this.recoveryActions,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: _getErrorColor(error.severity),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getErrorIcon(error.category)),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getErrorTitle(error),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: onDismiss,
                  ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              _getErrorMessage(error),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (recoveryActions.isNotEmpty) ...[
              SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: recoveryActions.map((action) {
                  return ActionChip(
                    avatar: Icon(action.icon, size: 18),
                    label: Text(action.title),
                    onPressed: () => _executeAction(action),
                    backgroundColor: action.isPrimary
                        ? Theme.of(context).primaryColor
                        : null,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return Colors.blue.shade50;
      case ErrorSeverity.warning:
        return Colors.orange.shade50;
      case ErrorSeverity.error:
        return Colors.red.shade50;
      case ErrorSeverity.critical:
        return Colors.red.shade100;
    }
  }

  IconData _getErrorIcon(ErrorCategory category) {
    switch (category) {
      case ErrorCategory.network:
        return Icons.wifi_off;
      case ErrorCategory.sync:
        return Icons.sync_problem;
      case ErrorCategory.validation:
        return Icons.warning;
      case ErrorCategory.permission:
        return Icons.lock;
      case ErrorCategory.storage:
        return Icons.storage;
      case ErrorCategory.platform:
        return Icons.error;
    }
  }
}
```

### Error Analytics and Monitoring

#### Error Tracking System
```dart
class ErrorTrackingService {
  final FirebaseAnalytics _analytics;
  final Map<String, int> _errorCounts = {};

  void trackError(CultureStackError error, {
    String? context,
    Map<String, dynamic>? metadata,
  }) {
    // Track error frequency
    final errorKey = '${error.category}_${error.runtimeType}';
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;

    // Log to analytics (without PII)
    _analytics.logEvent(
      name: 'error_occurred',
      parameters: {
        'error_category': error.category.name,
        'error_severity': error.severity.name,
        'error_type': error.runtimeType.toString(),
        'context': context ?? 'unknown',
        'occurrence_count': _errorCounts[errorKey],
        'user_type': _getUserType(),
        'app_version': _getAppVersion(),
        ...?metadata,
      },
    );
  }

  void trackRecoveryAttempt(RecoveryAction action, bool successful) {
    _analytics.logEvent(
      name: 'error_recovery_attempt',
      parameters: {
        'recovery_action': action.id,
        'recovery_type': action.type.name,
        'successful': successful,
      },
    );
  }

  Future<ErrorInsights> generateErrorInsights() async {
    return ErrorInsights(
      topErrors: _getTopErrors(),
      errorTrends: await _getErrorTrends(),
      recoverySuccessRates: await _getRecoverySuccessRates(),
    );
  }
}
```

### Error Prevention Strategies

#### Proactive Error Prevention
```dart
class ErrorPreventionService {
  final ConnectivityService _connectivity;
  final StorageService _storage;
  final QuotaTracker _quotaTracker;

  Future<PreventionResult> performPreventiveChecks() async {
    final issues = <PreventiveIssue>[];

    // Check storage space
    final storageInfo = await _storage.getStorageInfo();
    if (storageInfo.freeSpacePercent < 10) {
      issues.add(PreventiveIssue(
        type: IssueType.lowStorage,
        severity: IssueSeverity.warning,
        message: "Device storage is running low. Consider freeing up space to prevent save failures.",
        suggestedActions: [
          "Delete old photos or files",
          "Move photos to cloud storage",
          "Clear app cache",
        ],
      ));
    }

    // Check quota usage
    final quotaStatus = _quotaTracker.getCurrentStatus();
    if (quotaStatus.requestsUsed > 800) { // 80% of limit
      issues.add(PreventiveIssue(
        type: IssueType.approachingQuota,
        severity: IssueSeverity.info,
        message: "Approaching sync limits. We'll slow down uploads to prevent errors.",
        suggestedActions: [
          "Sync will continue more slowly",
          "Consider upgrading Google Drive storage",
        ],
      ));
    }

    // Check connectivity quality
    final connectivityQuality = await _connectivity.getConnectionQuality();
    if (connectivityQuality == ConnectionQuality.poor) {
      issues.add(PreventiveIssue(
        type: IssueType.poorConnection,
        severity: IssueSeverity.info,
        message: "Weak internet signal detected. Large uploads may be delayed.",
        suggestedActions: [
          "Connect to stronger WiFi if available",
          "Large photos will sync when signal improves",
        ],
      ));
    }

    return PreventionResult(issues: issues);
  }
}
```

This comprehensive error handling strategy ensures users receive clear, actionable guidance while maintaining confidence in data safety and app reliability.

## Data Models

Based on the PRD requirements, I'll define the core business entities for CultureStack's offline-first architecture with cloud synchronization.

### Culture Entity
**Purpose:** Represents the root culture record with enhanced search and performance capabilities.

**Key Attributes:**
- id: String (UUID) - Unique identifier for sync across devices
- cultureId: String - Human-readable culture ID (C001, C002, etc.)
- species: String - Plant species/variety name **[Indexed for search]**
- explantType: String - Type of plant tissue used
- sourcePlantId: String? - Optional source plant identifier
- initiationDate: LocalDate - Date culture was started **[Indexed for date queries]**
- mediumComposition: String - Medium recipe or manual composition
- recipeId: String? - Reference to saved recipe if used
- initialConditions: String - Environmental conditions at initiation
- status: CultureStatus - Current lifecycle status **[Indexed for timeline queries]**
- isDeleted: Boolean - Soft delete flag for sync
- createdAt: Instant - Record creation timestamp
- updatedAt: Instant - Last modification timestamp **[Indexed for timeline sorting]**
- syncVersion: Long - Version for conflict resolution
- lastSyncedAt: Instant? - **[Added]** Last successful cloud sync
- deviceId: String - **[Added]** Device that last modified record

#### Dart Data Class Interface
```dart
@freezed
class Culture with _$Culture {
  const factory Culture({
    @Default(Uuid().v4()) String id,
    required String cultureId,
    required String species,
    required String explantType,
    String? sourcePlantId,
    required DateTime initiationDate,
    required String mediumComposition,
    String? recipeId,
    required String initialConditions,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Culture;

  factory Culture.fromJson(Map<String, dynamic> json) => _$CultureFromJson(json);
}

@JsonEnum()
enum CultureStatus {
  @JsonValue('HEALTHY') healthy,
  @JsonValue('CONTAMINATED') contaminated,
  @JsonValue('READY_FOR_TRANSFER') readyForTransfer,
  @JsonValue('IN_ROOTING') inRooting,
  @JsonValue('ACCLIMATIZING') acclimatizing,
  @JsonValue('COMPLETED') completed,
  @JsonValue('DISPOSED') disposed,
}
```

#### Relationships
- One-to-many with Subculture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Subculture Entity
**Purpose:** Represents cultures derived from parent cultures, maintaining lineage traceability through the culture family tree.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- subcultureId: String - Human-readable ID (S001, S002, etc.)
- parentCultureId: String - Reference to parent culture ID
- subcultureDate: LocalDate - Date of subculturing
- mediumComposition: String - Medium used for subculture
- recipeId: String? - Recipe reference if used
- explantCount: Int - Number of explants transferred
- status: CultureStatus - Current status
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last update timestamp
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that last modified record

#### Dart Data Class Interface
```dart
@freezed
class Subculture with _$Subculture {
  const factory Subculture({
    @Default(Uuid().v4()) String id,
    required String subcultureId,
    required String parentCultureId,
    required DateTime subcultureDate,
    required String mediumComposition,
    String? recipeId,
    required int explantCount,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Subculture;

  factory Subculture.fromJson(Map<String, dynamic> json) => _$SubcultureFromJson(json);
}
```

#### Relationships
- Many-to-one with Culture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Recipe Entity (Enhanced for Discovery)
**Purpose:** Stores standardized medium formulations with enhanced discoverability for user workflows.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- name: String - User-defined recipe name **[Indexed for search]**
- description: String? - Optional recipe description
- ingredients: List<Ingredient> - Structured ingredient list
- preparationNotes: String? - Special preparation instructions
- category: String? - Optional categorization
- **plantTypes: List<String> - [Added] Target plant categories for filtering**
- **difficultyLevel: DifficultyLevel - [Added] Beginner/Intermediate/Advanced**
- **tags: List<String> - [Added] Searchable tags for recipe discovery**
- usageCount: Int - Track recipe popularity **[Indexed for recommendations]**
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last modification
- version: Int - Recipe version for history tracking
- syncVersion: Long - Sync conflict resolution
- **lastSyncedAt: Instant? - [Added] Sync metadata**
- **deviceId: String - [Added] Last modification device**

#### Dart Data Class Interface
```dart
@freezed
class Recipe with _$Recipe {
  const factory Recipe({
    @Default(Uuid().v4()) String id,
    required String name,
    String? description,
    required List<Ingredient> ingredients,
    String? preparationNotes,
    String? category,
    @Default([]) List<String> plantTypes,
    @Default(DifficultyLevel.beginner) DifficultyLevel difficultyLevel,
    @Default([]) List<String> tags,
    @Default(0) int usageCount,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int version,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Recipe;

  factory Recipe.fromJson(Map<String, dynamic> json) => _$RecipeFromJson(json);
}

@freezed
class Ingredient with _$Ingredient {
  const factory Ingredient({
    required String name,
    required String concentration,
    required String unit,
  }) = _Ingredient;

  factory Ingredient.fromJson(Map<String, dynamic> json) => _$IngredientFromJson(json);
}

@JsonEnum()
enum DifficultyLevel {
  @JsonValue('BEGINNER') beginner,
  @JsonValue('INTERMEDIATE') intermediate,
  @JsonValue('ADVANCED') advanced,
}
```

#### Relationships
- One-to-many with Culture (recipes used in cultures)
- One-to-many with Subculture (recipes used in subcultures)

### Observation Entity
**Purpose:** Captures monitoring data and photos for culture health tracking and decision-making support.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- cultureId: String - Reference to culture or subculture
- observationDate: LocalDate - Date of observation
- contaminationStatus: Boolean - Contamination present
- survivalStatus: SurvivalStatus - Overall health assessment
- growthStage: GrowthStage - Current development stage
- notes: String? - Additional observations
- photoFilenames: List<String> - Local photo file references
- createdAt: Instant - Record creation
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that created record

#### Dart Data Class Interface
```dart
@freezed
class Observation with _$Observation {
  const factory Observation({
    @Default(Uuid().v4()) String id,
    required String cultureId,
    required DateTime observationDate,
    required bool contaminationStatus,
    required SurvivalStatus survivalStatus,
    required GrowthStage growthStage,
    String? notes,
    @Default([]) List<String> photoFilenames,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Observation;

  factory Observation.fromJson(Map<String, dynamic> json) => _$ObservationFromJson(json);
}

@JsonEnum()
enum SurvivalStatus {
  @JsonValue('EXCELLENT') excellent,
  @JsonValue('GOOD') good,
  @JsonValue('FAIR') fair,
  @JsonValue('POOR') poor,
}

@JsonEnum()
enum GrowthStage {
  @JsonValue('INITIATION') initiation,
  @JsonValue('ESTABLISHMENT') establishment,
  @JsonValue('GROWTH') growth,
  @JsonValue('READY_FOR_TRANSFER') readyForTransfer,
}
```

#### Relationships
- Many-to-one with Culture or Subculture
- One-to-many with Photo files (stored separately)

### Photo Entity (New - Based on Journey Analysis)
**Purpose:** Manages culture photos with efficient storage and sync capabilities.

```kotlin
data class Photo(
    val id: String = UUID.randomUUID().toString(),
    val filename: String,
    val observationId: String,
    val localPath: String,
    val cloudPath: String? = null,
    val thumbnailPath: String, // For performance in gallery views
    val compressionLevel: CompressionLevel,
    val uploadStatus: UploadStatus,
    val fileSize: Long,
    val capturedAt: Instant,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CompressionLevel { ORIGINAL, HIGH, MEDIUM, LOW }
enum class UploadStatus { PENDING, UPLOADING, COMPLETED, FAILED }
```

### BatchOperation Entity (New - For Bulk Workflows)
**Purpose:** Supports bulk operations like creating multiple subcultures from user journey requirements.

```kotlin
data class BatchOperation(
    val id: String = UUID.randomUUID().toString(),
    val operationType: BatchOperationType,
    val parentId: String,
    val targetCount: Int,
    val completedCount: Int = 0,
    val status: BatchStatus,
    val parameters: Map<String, String> = emptyMap(),
    val createdAt: Instant = Instant.now(),
    val completedAt: Instant? = null
)

enum class BatchOperationType {
    CREATE_SUBCULTURES, UPDATE_STATUS, BULK_OBSERVATION
}
enum class BatchStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### SyncQueue Entity (New - Based on Data Flow Analysis)
**Purpose:** Manages offline-to-cloud synchronization with priority handling and retry logic.

```kotlin
data class SyncQueue(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String, // "Culture", "Recipe", "Observation", "Photo"
    val entityId: String,
    val operation: SyncOperation, // CREATE, UPDATE, DELETE
    val priority: SyncPriority, // HIGH, NORMAL, LOW
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val lastAttempt: Instant? = null,
    val status: SyncStatus, // PENDING, IN_PROGRESS, COMPLETED, FAILED
    val errorMessage: String? = null,
    val createdAt: Instant = Instant.now(),
    val scheduledFor: Instant = Instant.now()
)

enum class SyncOperation { CREATE, UPDATE, DELETE }
enum class SyncPriority { HIGH, NORMAL, LOW }
enum class SyncStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### ConflictResolution Entity (New - For Multi-Device Conflicts)
**Purpose:** Handles sync conflicts when same entity is modified on multiple devices.

```kotlin
data class ConflictResolution(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String,
    val entityId: String,
    val localVersion: String, // JSON snapshot of local entity
    val cloudVersion: String, // JSON snapshot of cloud entity
    val localUpdatedAt: Instant,
    val cloudUpdatedAt: Instant,
    val resolutionStrategy: ConflictStrategy,
    val userChoice: ConflictChoice? = null,
    val resolvedAt: Instant? = null,
    val createdAt: Instant = Instant.now()
)

enum class ConflictStrategy { MANUAL, LAST_WRITE_WINS, MERGE }
enum class ConflictChoice { ACCEPT_LOCAL, ACCEPT_CLOUD, MERGE }
```

### Database Indexes (Performance Optimization)
```sql
-- Critical indexes based on user journey analysis
CREATE INDEX idx_culture_timeline ON cultures(status, updatedAt DESC, isDeleted);
CREATE INDEX idx_culture_search ON cultures(species, status) WHERE isDeleted = 0;
CREATE INDEX idx_culture_initiation_date ON cultures(initiationDate);
CREATE INDEX idx_recipe_discovery ON recipes(plantTypes, difficultyLevel, usageCount DESC);
CREATE INDEX idx_recipe_usage_count ON recipes(usageCount DESC);
CREATE INDEX idx_sync_queue_processing ON sync_queue(status, priority, scheduledFor);
CREATE INDEX idx_batch_operation_tracking ON batch_operations(status, createdAt);
CREATE INDEX idx_photo_sync ON photos(uploadStatus, createdAt);
CREATE INDEX idx_observation_timeline ON observations(cultureId, observationDate DESC);
CREATE INDEX idx_conflict_resolution ON conflict_resolutions(entityType, resolvedAt);
```

## API Specification

Based on the architecture analysis, CultureStack is a **native Android application** with **local-first storage** and **Google Drive integration** for cloud sync. Unlike traditional REST/GraphQL APIs, this application uses Google APIs and local data access patterns.

**API Architecture Decision:**
Since CultureStack is not a web-based fullstack application but rather a Flutter mobile app, the "API" layer consists of:
1. **Local Data Access APIs** (Drift database interfaces)
2. **Google Services API Integration** (Drive, Auth, Billing via platform channels)
3. **Internal Service Layer APIs** (Business logic interfaces)
4. **Platform Channel APIs** (Native platform integration)

### Local Data Access API (Room Database)

#### Culture Management API
```dart
@DriftAccessor(tables: [Cultures])
class CultureDao extends DatabaseAccessor<AppDatabase> with _$CultureDaoMixin {
  CultureDao(AppDatabase db) : super(db);

  Stream<List<CultureData>> getAllActiveCultures() {
    return (select(cultures)
          ..where((c) => c.isDeleted.equals(false))
          ..orderBy([(c) => OrderingTerm.desc(c.updatedAt)]))
        .watch();
  }

  Future<List<CultureData>> getCulturesByStatus(CultureStatus status) {
    return (select(cultures)
          ..where((c) => c.status.equals(status.name) & c.isDeleted.equals(false)))
        .get();
  }

  Future<List<CultureData>> searchCulturesBySpecies(String query) {
    return (select(cultures)
          ..where((c) => c.species.contains(query) & c.isDeleted.equals(false)))
        .get();
  }

  Future<int> insertCulture(CulturesCompanion culture) {
    return into(cultures).insert(culture);
  }

  Future<bool> updateCulture(CultureData culture) {
    return update(cultures).replace(culture);
  }

  Future<int> softDeleteCulture(String id, DateTime deletedAt) {
    return (update(cultures)
          ..where((c) => c.id.equals(id)))
        .write(CulturesCompanion(
          isDeleted: const Value(true),
          updatedAt: Value(deletedAt),
        ));
  }
}
```

#### Recipe Management API
```dart
@DriftAccessor(tables: [Recipes])
class RecipeDao extends DatabaseAccessor<AppDatabase> with _$RecipeDaoMixin {
  RecipeDao(AppDatabase db) : super(db);

  Stream<List<RecipeData>> getAllRecipes() {
    return (select(recipes)
          ..where((r) => r.isDeleted.equals(false))
          ..orderBy([(r) => OrderingTerm.desc(r.usageCount)]))
        .watch();
  }

  Future<List<RecipeData>> getRecipesByPlantType(String plantType) {
    return (select(recipes)
          ..where((r) => r.plantTypes.contains(plantType) & r.isDeleted.equals(false)))
        .get();
  }

  Future<List<RecipeData>> getRecipesByDifficulty(DifficultyLevel level) {
    return (select(recipes)
          ..where((r) => r.difficultyLevel.equals(level.name) & r.isDeleted.equals(false)))
        .get();
  }

  Future<int> insertRecipe(RecipesCompanion recipe) {
    return into(recipes).insert(recipe);
  }

  Future<int> incrementUsageCount(String id) {
    return (update(recipes)
          ..where((r) => r.id.equals(id)))
        .write(RecipesCompanion(
          usageCount: Value(recipes.usageCount + 1),
        ));
  }
}
```

### Google Services Integration API

#### Enhanced Google Drive Sync Service (Rate-Limited Implementation)
```dart
abstract class ConstraintAwareDriveService {
  Future<Result<SyncResult>> syncWithRateLimit(List<SyncOperation> operations);
  Future<Result<QuotaStatus>> estimateRemainingQuota();
  Future<Result<void>> scheduleDelayedSync(Duration delay);

  // Batch operations to respect API limits
  Future<Result<BatchSyncResult>> batchSyncCultures(List<Culture> cultures);
  Future<Result<PhotoSyncResult>> intelligentPhotoSync();

  // Graceful degradation
  Future<Result<void>> enableOfflineMode();
  Future<ServicesStatus> detectServiceAvailability();
}

class GoogleDriveService implements ConstraintAwareDriveService {
  final GoogleDriveApi _driveApi;
  final DriveApiRateLimiter _rateLimiter;
  final SyncPriorityQueue _priorityQueue;
  final QuotaTracker _quotaTracker;

  // Google Drive API Quotas (per user):
  // - 1,000 requests per 100 seconds
  // - 100 queries per second
  // - 750 MB uploads per day for free users
  static const int _maxRequestsPer100Seconds = 1000;
  static const int _maxQueriesPerSecond = 100;
  static const int _maxDailyUploadMB = 750;

  GoogleDriveService(this._driveApi, this._rateLimiter, this._priorityQueue, this._quotaTracker);

  @override
  Future<Result<SyncResult>> syncWithRateLimit(List<SyncOperation> operations) async {
    try {
      // Pre-sync quota check
      final quotaStatus = await _quotaTracker.getCurrentStatus();
      if (quotaStatus.isExhausted) {
        return Result.error(CultureStackError.quotaError(quotaStatus.resetTime));
      }

      // Priority-based operation sorting
      final prioritizedOps = _priorityQueue.sortByPriority(operations);
      final results = <OperationResult>[];
      var successCount = 0;
      var rateLimitHit = false;

      for (final operation in prioritizedOps) {
        // Check if we can proceed with this operation
        if (!await _rateLimiter.canProceed(operation.estimatedCost)) {
          // Queue remaining operations for later
          await _queueRemainingOperations(prioritizedOps.skip(results.length));
          rateLimitHit = true;
          break;
        }

        // Execute the operation
        final result = await _executeOperation(operation);
        results.add(result);

        if (result.success) {
          successCount++;
          _quotaTracker.recordSuccess(operation);
        } else {
          _quotaTracker.recordFailure(operation);

          // Handle rate limit response from server
          if (result.isRateLimitError) {
            await _handleRateLimitResponse(result);
            rateLimitHit = true;
            break;
          }
        }
      }

      return Result.success(SyncResult(
        totalOperations: operations.length,
        successfulOperations: successCount,
        failedOperations: results.length - successCount,
        rateLimitHit: rateLimitHit,
        nextAllowedSync: rateLimitHit ? _rateLimiter.nextAvailableTime : null,
      ));

    } catch (e) {
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }

  @override
  Future<Result<BatchSyncResult>> batchSyncCultures(List<Culture> cultures) async {
    // Intelligent batching based on quota availability
    final quotaStatus = await _quotaTracker.getCurrentStatus();
    final batchSize = _calculateOptimalBatchSize(quotaStatus, cultures.length);

    final batches = _splitIntoBatches(cultures, batchSize);
    var totalSuccess = 0;
    var totalFailed = 0;
    var rateLimitEncountered = false;

    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];

      // Create batch upload request (reduces API calls)
      final batchRequest = _createBatchRequest(batch);

      if (!await _rateLimiter.canProceed(batchRequest.estimatedCost)) {
        // Schedule remaining batches for later
        await _scheduleDelayedBatches(batches.skip(i), Duration(minutes: 2));
        rateLimitEncountered = true;
        break;
      }

      final result = await _executeBatchRequest(batchRequest);
      totalSuccess += result.successCount;
      totalFailed += result.failureCount;

      // Add delay between batches to respect rate limits
      if (i < batches.length - 1) {
        await Future.delayed(Duration(milliseconds: 200));
      }
    }

    return Result.success(BatchSyncResult(
      successCount: totalSuccess,
      failedCount: totalFailed,
      rateLimitHit: rateLimitEncountered,
      nextAllowedSync: rateLimitEncountered ? _rateLimiter.nextAvailableTime : null,
    ));
  }

  @override
  Future<Result<PhotoSyncResult>> intelligentPhotoSync() async {
    try {
      final pendingPhotos = await _getPendingPhotoUploads();
      final quotaStatus = await _quotaTracker.getCurrentStatus();

      // Calculate how many photos we can upload within quota
      final availableUploadMB = quotaStatus.remainingUploadQuotaMB;
      final photosToSync = _selectPhotosForSync(pendingPhotos, availableUploadMB);

      if (photosToSync.isEmpty) {
        return Result.success(PhotoSyncResult(
          photosUploaded: 0,
          quotaExhausted: true,
          nextUploadWindow: quotaStatus.resetTime,
        ));
      }

      var uploadedCount = 0;
      for (final photo in photosToSync) {
        if (!await _rateLimiter.canProceed(photo.estimatedApiCost)) {
          break;
        }

        final result = await _uploadPhoto(photo);
        if (result.success) {
          uploadedCount++;
          _quotaTracker.recordUpload(photo.compressedSizeMB);
        }

        // Progressive delay to avoid burst limit
        await Future.delayed(Duration(milliseconds: 500));
      }

      return Result.success(PhotoSyncResult(
        photosUploaded: uploadedCount,
        quotaExhausted: false,
        nextUploadWindow: null,
      ));

    } catch (e) {
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }

  int _calculateOptimalBatchSize(QuotaStatus quotaStatus, int totalItems) {
    final requestsAvailable = quotaStatus.requestsRemaining;
    final maxBatchSize = 100; // Google Drive batch limit

    // Conservative estimate: each culture sync = 2 API calls (metadata + data)
    final maxItemsForQuota = (requestsAvailable / 2).floor();

    return math.min(maxBatchSize, math.min(maxItemsForQuota, totalItems));
  }

  Future<void> _handleRateLimitResponse(OperationResult result) async {
    final retryAfter = result.retryAfterSeconds ?? 60;
    await _rateLimiter.recordRateLimitHit(Duration(seconds: retryAfter));

    // Notify user about rate limit with helpful message
    _notifyUserOfRateLimit(retryAfter);
  }

  void _notifyUserOfRateLimit(int retryAfterSeconds) {
    final message = retryAfterSeconds < 60
        ? "Sync paused briefly due to high activity. Retrying in ${retryAfterSeconds}s."
        : "Sync paused due to Google Drive limits. Retrying in ${(retryAfterSeconds / 60).ceil()} minutes.";

    // Emit notification event
    _eventBus.fire(SyncNotificationEvent(
      type: NotificationType.rateLimitWarning,
      message: message,
      retryAfter: Duration(seconds: retryAfterSeconds),
    ));
  }
}

class DriveApiRateLimiter {
  final TokenBucket _requestBucket;
  final TokenBucket _queryBucket;
  DateTime? _lastRateLimitHit;
  Duration? _rateLimitDuration;

  DriveApiRateLimiter()
    : _requestBucket = TokenBucket(1000, Duration(seconds: 100)), // 1000 requests per 100s
      _queryBucket = TokenBucket(100, Duration(seconds: 1));       // 100 queries per second

  Future<bool> canProceed(int estimatedCost) async {
    // Check if we're in rate limit cooldown
    if (_isInRateLimitCooldown()) {
      return false;
    }

    // Check both token buckets
    return _requestBucket.tryConsume(estimatedCost) &&
           _queryBucket.tryConsume(1);
  }

  Future<void> recordRateLimitHit(Duration duration) async {
    _lastRateLimitHit = DateTime.now();
    _rateLimitDuration = duration;

    // Reset token buckets to be conservative
    _requestBucket.reset();
    _queryBucket.reset();
  }

  bool _isInRateLimitCooldown() {
    if (_lastRateLimitHit == null || _rateLimitDuration == null) {
      return false;
    }

    final cooldownEnd = _lastRateLimitHit!.add(_rateLimitDuration!);
    return DateTime.now().isBefore(cooldownEnd);
  }

  DateTime? get nextAvailableTime {
    if (_isInRateLimitCooldown()) {
      return _lastRateLimitHit!.add(_rateLimitDuration!);
    }
    return null;
  }
}

class TokenBucket {
  final int capacity;
  final Duration refillPeriod;
  int _tokens;
  DateTime _lastRefill;

  TokenBucket(this.capacity, this.refillPeriod)
    : _tokens = capacity,
      _lastRefill = DateTime.now();

  bool tryConsume(int tokens) {
    _refillIfNeeded();

    if (_tokens >= tokens) {
      _tokens -= tokens;
      return true;
    }
    return false;
  }

  void _refillIfNeeded() {
    final now = DateTime.now();
    final timePassed = now.difference(_lastRefill);

    if (timePassed >= refillPeriod) {
      _tokens = capacity;
      _lastRefill = now;
    }
  }

  void reset() {
    _tokens = 0;
    _lastRefill = DateTime.now().add(refillPeriod);
  }
}

class QuotaTracker {
  int _requestsUsed = 0;
  int _uploadMBUsed = 0;
  DateTime _quotaPeriodStart = DateTime.now();

  static const Duration _quotaPeriod = Duration(hours: 24);

  QuotaStatus getCurrentStatus() {
    _resetIfNewPeriod();

    return QuotaStatus(
      requestsUsed: _requestsUsed,
      requestsRemaining: 1000 - _requestsUsed,
      resetTime: _quotaPeriodStart.add(_quotaPeriod),
      storageUsed: _uploadMBUsed,
      storageRemaining: 750 - _uploadMBUsed,
      recommendedSyncDelay: _calculateRecommendedDelay(),
    );
  }

  void recordSuccess(SyncOperation operation) {
    _requestsUsed += operation.estimatedCost;
  }

  void recordUpload(double sizeMB) {
    _uploadMBUsed += sizeMB.ceil();
  }

  Duration _calculateRecommendedDelay() {
    final quotaUsagePercent = _requestsUsed / 1000.0;

    if (quotaUsagePercent > 0.9) {
      return Duration(minutes: 10); // Very conservative near limit
    } else if (quotaUsagePercent > 0.7) {
      return Duration(minutes: 2);  // Moderate delay
    } else {
      return Duration(seconds: 30); // Minimal delay
    }
  }

  void _resetIfNewPeriod() {
    final now = DateTime.now();
    if (now.difference(_quotaPeriodStart) >= _quotaPeriod) {
      _requestsUsed = 0;
      _uploadMBUsed = 0;
      _quotaPeriodStart = now;
    }
  }

  bool get isExhausted => _requestsUsed >= 950 || _uploadMBUsed >= 700; // 95% threshold
}
```

@freezed
class QuotaStatus with _$QuotaStatus {
  const factory QuotaStatus({
    required int requestsUsed,
    required int requestsRemaining,
    required DateTime resetTime,
    required int storageUsed,
    required int storageRemaining,
    required Duration recommendedSyncDelay,
  }) = _QuotaStatus;

  factory QuotaStatus.fromJson(Map<String, dynamic> json) => _$QuotaStatusFromJson(json);
}

@freezed
class BatchSyncResult with _$BatchSyncResult {
  const factory BatchSyncResult({
    required int successCount,
    required int failedCount,
    required bool rateLimitHit,
    DateTime? nextAllowedSync,
  }) = _BatchSyncResult;

  factory BatchSyncResult.fromJson(Map<String, dynamic> json) => _$BatchSyncResultFromJson(json);
}
```

#### Authentication Service
```dart
abstract class AuthService {
  Future<Result<GoogleSignInAccount>> signInWithGoogle();
  Future<Result<void>> signOut();
  Future<GoogleSignInAccount?> getCurrentUser();
  Future<Result<void>> refreshTokenIfNeeded();
  bool isUserSignedIn();
}

class GoogleAuthService implements AuthService {
  final GoogleSignIn _googleSignIn;

  GoogleAuthService(this._googleSignIn);

  @override
  Future<Result<GoogleSignInAccount>> signInWithGoogle() async {
    try {
      final account = await _googleSignIn.signIn();
      if (account != null) {
        return Result.success(account);
      }
      return Result.error(AuthenticationCancelledException());
    } catch (e) {
      return Result.error(AuthenticationException(e.toString()));
    }
  }
}
```

#### Billing Service
```dart
abstract class BillingService {
  Future<Result<void>> initializeBilling();
  Future<Result<List<ProductDetails>>> querySubscriptions();
  Future<Result<PurchaseDetails>> purchaseSubscription(String productId);
  Future<Result<bool>> verifyPurchase(PurchaseDetails purchase);
  bool isPremiumUser();
}

class InAppPurchaseService implements BillingService {
  final InAppPurchase _inAppPurchase;

  InAppPurchaseService(this._inAppPurchase);

  @override
  Future<Result<void>> initializeBilling() async {
    final available = await _inAppPurchase.isAvailable();
    if (available) {
      return Result.success(null);
    }
    return Result.error(BillingUnavailableException());
  }
}
```

### Enhanced Offline Queue Service
```dart
abstract class OfflineQueueService {
  Future<Result<void>> queueOperation(SyncOperation operation, SyncPriority priority);
  Future<int> getQueueSize();
  Future<QueueStatus> getQueueStatus();
  Future<Result<SyncResult>> processQueueWhenOnline();
  Future<Result<void>> compactQueue();

  // Emergency capabilities
  Future<Result<String>> exportQueueForManualSync();
  Future<Result<void>> importQueueFromBackup(String data);
}

class SqfliteOfflineQueueService implements OfflineQueueService {
  final SyncQueueDao _syncQueueDao;

  SqfliteOfflineQueueService(this._syncQueueDao);

  @override
  Future<Result<void>> queueOperation(SyncOperation operation, SyncPriority priority) async {
    try {
      await _syncQueueDao.insertSyncOperation(operation, priority);
      return Result.success(null);
    } catch (e) {
      return Result.error(QueueOperationException(e.toString()));
    }
  }
}

@freezed
class QueueStatus with _$QueueStatus {
  const factory QueueStatus({
    required int pendingOperations,
    required Duration estimatedSyncTime,
    DateTime? lastSuccessfulSync,
    DateTime? oldestPendingOperation,
  }) = _QueueStatus;

  factory QueueStatus.fromJson(Map<String, dynamic> json) => _$QueueStatusFromJson(json);
}
```

### Graceful Degradation Service (Enhanced)
```dart
abstract class GracefulDegradationService {
  Future<ServicesStatus> detectGoogleServicesAvailability();
  Future<Result<void>> enableGuestMode();
  Future<Result<void>> enableOfflineOnlyMode();
  Future<Result<ExportResult>> exportLocalData();

  // Fallback authentication
  Future<Result<LocalAccount>> createLocalAccount(String username);
  Future<Result<MigrationResult>> migrateToGoogleAccount(LocalAccount localAccount);

  // Regional fallback strategies
  Future<Result<RegionalConfig>> detectRegionalConstraints();
  Future<Result<void>> enableRegionalFallbackMode(RegionalConfig config);
  Future<Result<AlternativeSync>> setupAlternativeSyncMethod();
}

class FlutterGracefulDegradationService implements GracefulDegradationService {
  final SharedPreferences _prefs;
  final DatabaseService _databaseService;
  final RegionalDetectionService _regionalService;

  FlutterGracefulDegradationService(this._prefs, this._databaseService, this._regionalService);

  @override
  Future<ServicesStatus> detectGoogleServicesAvailability() async {
    try {
      // Multi-tier detection strategy
      final playServicesAvailable = await _checkGooglePlayServices();
      final driveApiAccessible = await _testDriveApiAccess();
      final billingAvailable = await _checkPlayBilling();

      if (playServicesAvailable && driveApiAccessible && billingAvailable) {
        return ServicesStatus.available;
      } else if (playServicesAvailable) {
        return ServicesStatus.partial;
      } else {
        return ServicesStatus.unavailable;
      }
    } catch (e) {
      return ServicesStatus.unavailable;
    }
  }

  @override
  Future<Result<RegionalConfig>> detectRegionalConstraints() async {
    try {
      final region = await _regionalService.detectUserRegion();
      final constraints = _getRegionalConstraints(region);
      return Result.success(constraints);
    } catch (e) {
      return Result.error(CultureStackError.networkUnavailable());
    }
  }

  @override
  Future<Result<void>> enableRegionalFallbackMode(RegionalConfig config) async {
    try {
      // Configure app for regional limitations
      await _prefs.setBool('google_services_restricted', config.googleServicesBlocked);
      await _prefs.setBool('offline_only_mode', config.requiresOfflineOnly);
      await _prefs.setString('fallback_auth_method', config.fallbackAuthMethod);

      // Initialize alternative services if needed
      if (config.requiresAlternativeSync) {
        await setupAlternativeSyncMethod();
      }

      return Result.success(null);
    } catch (e) {
      return Result.error(CultureStackError.databaseError(e.toString()));
    }
  }

  @override
  Future<Result<AlternativeSync>> setupAlternativeSyncMethod() async {
    try {
      // Fallback 1: Local file export/import
      final localExportService = LocalFileExportService();
      await localExportService.initialize();

      // Fallback 2: QR code-based sync for small datasets
      final qrSyncService = QRCodeSyncService();
      await qrSyncService.initialize();

      return Result.success(AlternativeSync(
        exportService: localExportService,
        qrService: qrSyncService,
        method: SyncMethod.localFile,
      ));
    } catch (e) {
      return Result.error(CultureStackError.databaseError(e.toString()));
    }
  }

  RegionalConfig _getRegionalConstraints(String region) {
    final constraints = {
      'CN': RegionalConfig(
        region: 'China',
        googleServicesBlocked: true,
        requiresOfflineOnly: true,
        requiresAlternativeSync: true,
        fallbackAuthMethod: 'local_account',
        supportedFeatures: ['offline_tracking', 'local_export'],
      ),
      'IR': RegionalConfig(
        region: 'Iran',
        googleServicesBlocked: true,
        requiresOfflineOnly: true,
        requiresAlternativeSync: true,
        fallbackAuthMethod: 'local_account',
        supportedFeatures: ['offline_tracking', 'local_export'],
      ),
      'RU': RegionalConfig(
        region: 'Russia',
        googleServicesBlocked: false,
        requiresOfflineOnly: false,
        requiresAlternativeSync: true, // Backup option
        fallbackAuthMethod: 'google_or_local',
        supportedFeatures: ['limited_google_services', 'offline_tracking', 'local_export'],
      ),
    };

    return constraints[region] ?? RegionalConfig.unrestricted();
  }
}

@freezed
class RegionalConfig with _$RegionalConfig {
  const factory RegionalConfig({
    required String region,
    required bool googleServicesBlocked,
    required bool requiresOfflineOnly,
    required bool requiresAlternativeSync,
    required String fallbackAuthMethod,
    required List<String> supportedFeatures,
  }) = _RegionalConfig;

  factory RegionalConfig.unrestricted() => const RegionalConfig(
    region: 'Global',
    googleServicesBlocked: false,
    requiresOfflineOnly: false,
    requiresAlternativeSync: false,
    fallbackAuthMethod: 'google',
    supportedFeatures: ['full_functionality'],
  );

  factory RegionalConfig.fromJson(Map<String, dynamic> json) => _$RegionalConfigFromJson(json);
}

@freezed
class AlternativeSync with _$AlternativeSync {
  const factory AlternativeSync({
    required LocalFileExportService exportService,
    required QRCodeSyncService qrService,
    required SyncMethod method,
  }) = _AlternativeSync;
}

@JsonEnum()
enum SyncMethod {
  @JsonValue('GOOGLE_DRIVE') googleDrive,
  @JsonValue('LOCAL_FILE') localFile,
  @JsonValue('QR_CODE') qrCode,
  @JsonValue('MANUAL_EXPORT') manualExport,
}
```

@JsonEnum()
enum ServicesStatus {
  @JsonValue('AVAILABLE') available,
  @JsonValue('OFFLINE') offline,
  @JsonValue('NOT_AUTHENTICATED') notAuthenticated,
  @JsonValue('UNAVAILABLE') unavailable,
  @JsonValue('RATE_LIMITED') rateLimited,
  @JsonValue('QUOTA_EXCEEDED') quotaExceeded,
}

@freezed
class ExportResult with _$ExportResult {
  const factory ExportResult({
    required String filePath,
    required ExportFormat format, // JSON, CSV, PDF
    required bool includesPhotos,
    required int fileSize,
  }) = _ExportResult;

  factory ExportResult.fromJson(Map<String, dynamic> json) => _$ExportResultFromJson(json);
}

@JsonEnum()
enum ExportFormat {
  @JsonValue('JSON') json,
  @JsonValue('CSV') csv,
  @JsonValue('PDF') pdf,
}
```

### Internal Service Layer API

#### Culture Management Service
```dart
abstract class CultureService {
  Future<Result<Culture>> createCulture(CreateCultureRequest cultureRequest);
  Future<Result<Subculture>> createSubculture(CreateSubcultureRequest subcultureRequest);
  Future<Result<Observation>> addObservation(AddObservationRequest observationRequest);
  Future<Result<void>> updateCultureStatus(String cultureId, CultureStatus status);
  Future<Result<CultureLineage>> getCultureLineage(String cultureId);
  Future<Result<BatchOperation>> createBatchSubcultures(BatchSubcultureRequest batchRequest);
}

class CultureServiceImpl implements CultureService {
  final CultureDao _cultureDao;
  final SyncQueueService _syncQueueService;
  final EventBus _eventBus;

  CultureServiceImpl(this._cultureDao, this._syncQueueService, this._eventBus);

  @override
  Future<Result<Culture>> createCulture(CreateCultureRequest request) async {
    try {
      final culture = Culture(
        cultureId: request.cultureId,
        species: request.species,
        explantType: request.explantType,
        // ... other fields
      );

      await _cultureDao.insertCulture(culture.toCompanion());
      _eventBus.fire(CultureCreatedEvent(culture));
      await _syncQueueService.queueForSync(culture);

      return Result.success(culture);
    } catch (e) {
      return Result.error(CultureCreationException(e.toString()));
    }
  }
}
```

@freezed
class CreateCultureRequest with _$CreateCultureRequest {
  const factory CreateCultureRequest({
    required String species,
    required String explantType,
    String? sourcePlantId,
    required DateTime initiationDate,
    required String mediumComposition,
    String? recipeId,
    required String initialConditions,
  }) = _CreateCultureRequest;

  factory CreateCultureRequest.fromJson(Map<String, dynamic> json) => _$CreateCultureRequestFromJson(json);
}
```

### Error Handling (Enhanced with Constraints)
```dart
@freezed
class CultureStackError with _$CultureStackError implements Exception {
  const factory CultureStackError.networkUnavailable() = NetworkUnavailable;
  const factory CultureStackError.googleServicesUnavailable() = GoogleServicesUnavailable;
  const factory CultureStackError.storageQuotaExceeded() = StorageQuotaExceeded;
  const factory CultureStackError.rateLimitExceeded() = RateLimitExceeded; // New
  const factory CultureStackError.syncConflictDetected() = SyncConflictDetected;
  const factory CultureStackError.localStorageFull() = LocalStorageFull; // New
  const factory CultureStackError.databaseError(String cause) = DatabaseError;
  const factory CultureStackError.googleApiError(int code, String message) = GoogleApiError;
  const factory CultureStackError.quotaError(DateTime resetTime) = QuotaError; // New

  factory CultureStackError.fromJson(Map<String, dynamic> json) => _$CultureStackErrorFromJson(json);
}

// Result type for consistent error handling
@freezed
class Result<T> with _$Result<T> {
  const factory Result.success(T data) = Success<T>;
  const factory Result.error(CultureStackError error) = Error<T>;

  factory Result.fromJson(Map<String, dynamic> json, T Function(Object?) fromJsonT) => _$ResultFromJson(json, fromJsonT);
}
```

## Components

Based on the architectural patterns, tech stack, and constraint analysis, I'll define the major logical components across CultureStack's Flutter cross-platform architecture.

### Culture Management Component
**Responsibility:** Handles all culture and subculture lifecycle operations including creation, monitoring, status updates, and lineage tracking.

**Key Interfaces:**
- `CultureService.createCulture(CreateCultureRequest): Result<Culture>`
- `CultureService.createSubculture(CreateSubcultureRequest): Result<Subculture>`
- `CultureService.updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>`
- `CultureService.getCultureLineage(cultureId: String): Result<CultureLineage>`

**Dependencies:** Database Component, Sync Component, Notification Component

**Technology Stack:**
- Dart with async/await for async operations
- Drift database for local storage
- GetIt + Injectable for dependency injection
- Flutter Workmanager for background status updates

### Recipe Management Component
**Responsibility:** Manages recipe library operations including creation, search, categorization, and usage tracking for medium formulations.

**Key Interfaces:**
- `RecipeService.createRecipe(CreateRecipeRequest): Result<Recipe>`
- `RecipeService.searchRecipes(searchCriteria: RecipeSearchCriteria): Result<List<Recipe>>`
- `RecipeService.getRecipesByPlantType(plantType: String): Result<List<Recipe>>`
- `RecipeService.incrementUsageCount(recipeId: String): Result<Unit>`

**Dependencies:** Database Component, Search Component

**Technology Stack:**
- Drift database with custom FTS implementation
- json_annotation for structured ingredient data serialization
- Repository pattern for data access abstraction

### Observation & Photo Component
**Responsibility:** Handles observation logging, photo capture, compression, storage, and gallery management for culture documentation.

**Key Interfaces:**
- `ObservationService.addObservation(AddObservationRequest): Result<Observation>`
- `PhotoService.captureAndProcessPhoto(cultureId: String): Result<Photo>`
- `PhotoService.compressPhoto(photo: Photo, level: CompressionLevel): Result<OptimizedPhoto>`
- `PhotoService.getPhotoGallery(cultureId: String): Result<List<Photo>>`

**Dependencies:** Database Component, File Storage Component, Sync Component

**Technology Stack:**
- Flutter Camera plugin for photo capture
- cached_network_image for image loading and caching
- flutter_image_compress for optimal compression
- path_provider for cross-platform file management

### Sync Management Component
**Responsibility:** Orchestrates data synchronization between local SQLite storage and Google Drive, handling conflicts, rate limits, and offline scenarios.

**Key Interfaces:**
- `SyncService.queueForSync(entityType: String, entityId: String, operation: SyncOperation): Result<Unit>`
- `SyncService.performIntelligentSync(): Result<SyncResult>`
- `SyncService.resolveConflict(conflictId: String, resolution: ConflictChoice): Result<Unit>`
- `SyncService.enableOfflineMode(): Result<Unit>`

**Dependencies:** Google Services Component, Database Component, Queue Management Component

**Technology Stack:**
- googleapis package for Google Drive API v3
- Flutter Workmanager for background sync scheduling
- Dio for HTTP communication
- Rate limiting with custom Dart implementation

### Authentication & Billing Component
**Responsibility:** Manages Google account authentication, premium subscription verification, and user session state for freemium model support.

**Key Interfaces:**
- `AuthService.signInWithGoogle(): Result<GoogleSignInAccount>`
- `BillingService.purchaseSubscription(subscriptionId: String): Result<PurchaseResult>`
- `BillingService.verifyPremiumStatus(): Result<Boolean>`
- `AuthService.enableGuestMode(): Result<GuestAccount>`

**Dependencies:** Google Services Component, Database Component

**Technology Stack:**
- google_sign_in plugin for authentication
- in_app_purchase plugin for subscriptions
- shared_preferences for session persistence
- firebase_analytics for user behavior tracking

### Notification & Scheduling Component
**Responsibility:** Manages reminder scheduling, push notifications, calendar integration, and background task coordination for culture maintenance.

**Key Interfaces:**
- `NotificationService.scheduleReminder(reminderId: String, scheduledTime: Instant): Result<Unit>`
- `NotificationService.sendPushNotification(notification: CultureNotification): Result<Unit>`
- `SchedulingService.createRecurringReminder(reminder: RecurringReminder): Result<Unit>`
- `SchedulingService.getUpcomingTasks(timeRange: TimeRange): Result<List<ScheduledTask>>`

**Dependencies:** Database Component, Culture Management Component

**Technology Stack:**
- firebase_messaging for push notifications
- flutter_local_notifications for local scheduling
- Flutter Workmanager for background task execution
- Platform-specific notification channels via method channels

### Database Component
**Responsibility:** Provides unified data access layer with offline-first architecture, including local storage, caching, indexing, and transaction management.

**Key Interfaces:**
- `DatabaseService.executeInTransaction(block: suspend () -> Unit): Result<Unit>`
- `DatabaseService.getEntityById(entityType: String, id: String): Result<Any?>`
- `DatabaseService.performMaintenance(): Result<MaintenanceResult>`
- `DatabaseService.exportDatabase(format: ExportFormat): Result<String>`

**Dependencies:** None (foundational layer)

**Technology Stack:**
- SQLite with Drift ORM for local storage
- sqlcipher_flutter for database encryption
- Database migration strategies with Drift
- Optimized indexes for query performance

### Google Services Integration Component
**Responsibility:** Abstracts Google API interactions including Drive storage, authentication, billing, and service availability detection with graceful degradation.

**Key Interfaces:**
- `GoogleServicesManager.checkServiceAvailability(): ServicesStatus`
- `GoogleServicesManager.initializeServices(): Result<Unit>`
- `GoogleServicesManager.handleServiceUnavailable(): Result<DegradationStrategy>`
- `GoogleServicesManager.getQuotaStatus(): Result<QuotaStatus>`

**Dependencies:** None (external integration layer)

**Technology Stack:**
- Platform channels for Google Play Services integration
- googleapis package for Google Drive API v3
- google_sign_in plugin for authentication
- Custom availability checker via method channels

### Component Interaction Diagram (Optimized for Performance)
```mermaid
graph TB
    subgraph "Presentation Layer (Flutter Widgets)"
        A[Culture Screens]
        B[Recipe Screens]
        C[Calendar Screens]
    end

    subgraph "Business Logic Layer (Bloc/Cubit)"
        D[Culture Bloc]
        E[Recipe Bloc]
        F[Observation Bloc]
        G[Notification Cubit]
    end

    subgraph "Data Layer"
        H[Sync Management]
        I[Database Repository]
        J[Google Services Repository]
        K[Event Bus (Streams)]
    end

    A --> D
    B --> E
    C --> G
    D --> I
    E --> I
    F --> I
    G --> I

    H --> I
    H --> J

    D -.->|stream events| K
    F -.->|stream events| K
    E -.->|stream events| K
    K -.->|notifications| G
    K -.->|sync requests| H
    K -.->|state updates| D

    J -.->|circuit breaker| H
```

### Enhanced Component Architecture (Constraint-Aware)

#### Event-Driven Communication System (Flutter Streams)
```dart
abstract class ComponentEventBus {
  void publish(ComponentEvent event);
  Stream<T> subscribe<T extends ComponentEvent>();
  void publishAndForget(ComponentEvent event); // Fire-and-forget for performance
}

@freezed
class ComponentEvent with _$ComponentEvent {
  const factory ComponentEvent.cultureCreated(Culture culture) = CultureCreatedEvent;
  const factory ComponentEvent.observationAdded(Observation observation) = ObservationAddedEvent;
  const factory ComponentEvent.syncCompleted(String entityType, String entityId) = SyncCompletedEvent;
  const factory ComponentEvent.conflictDetected(ConflictResolution conflict) = ConflictDetectedEvent;

  factory ComponentEvent.fromJson(Map<String, dynamic> json) => _$ComponentEventFromJson(json);
}

class StreamEventBus implements ComponentEventBus {
  final StreamController<ComponentEvent> _controller = StreamController.broadcast();

  @override
  void publish(ComponentEvent event) {
    _controller.add(event);
  }

  @override
  Stream<T> subscribe<T extends ComponentEvent>() {
    return _controller.stream.where((event) => event is T).cast<T>();
  }

  @override
  void publishAndForget(ComponentEvent event) {
    _controller.add(event);
  }
}
```

#### Dependency Management with Circuit Breaker
```dart
class GoogleServicesCircuitBreaker {
  final int failureThreshold;
  final Duration timeout;

  int _failures = 0;
  DateTime? _lastFailureTime;
  CircuitState _state = CircuitState.closed;

  GoogleServicesCircuitBreaker({
    this.failureThreshold = 5,
    this.timeout = const Duration(seconds: 30),
  });

  Future<Result<T>> execute<T>(Future<T> Function() operation) async {
    switch (_state) {
      case CircuitState.open:
        if (_lastFailureTime != null &&
            DateTime.now().difference(_lastFailureTime!) > timeout) {
          _state = CircuitState.halfOpen;
          return _tryOperation(operation);
        } else {
          return Result.error(CultureStackError.googleServicesUnavailable());
        }
      case CircuitState.halfOpen:
      case CircuitState.closed:
        return _tryOperation(operation);
    }
  }

  Future<Result<T>> _tryOperation<T>(Future<T> Function() operation) async {
    try {
      final result = await operation();
      _onSuccess();
      return Result.success(result);
    } catch (e) {
      _onFailure();
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }
}

enum CircuitState { closed, open, halfOpen }
```

#### Resource Management Strategy
```dart
class ComponentResourceManager {
  // Flutter uses isolates for compute-intensive operations
  static const int _maxConcurrentPhotoOperations = 2;
  static const int _maxConcurrentSyncOperations = 3;

  final Semaphore _photoSemaphore = Semaphore(_maxConcurrentPhotoOperations);
  final Semaphore _syncSemaphore = Semaphore(_maxConcurrentSyncOperations);

  Future<T> executePhotoOperation<T>(Future<T> Function() operation) async {
    await _photoSemaphore.acquire();
    try {
      // Use compute for CPU-intensive photo operations
      return await compute(_executeInIsolate, operation);
    } finally {
      _photoSemaphore.release();
    }
  }

  Future<T> executeSyncOperation<T>(Future<T> Function() operation) async {
    await _syncSemaphore.acquire();
    try {
      return await operation();
    } finally {
      _syncSemaphore.release();
    }
  }

  ComponentPriority prioritizeComponents(SystemState systemState) {
    if (systemState.isLowMemory) return ComponentPriority.essentialOnly;
    if (systemState.isBatteryLow) return ComponentPriority.reduceBackground;
    if (systemState.isNetworkLimited) return ComponentPriority.offlineFocus;
    return ComponentPriority.fullFunctionality;
  }

  static Future<T> _executeInIsolate<T>(Future<T> Function() operation) async {
    return await operation();
  }
}

class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
```

#### Performance-Optimized Component Communication (Flutter Bloc)
```dart
class CultureBloc extends Bloc<CultureEvent, CultureState> {
  final CultureRepository _cultureRepository;
  final ComponentEventBus _eventBus;

  CultureBloc(this._cultureRepository, this._eventBus) : super(const CultureState.initial()) {
    on<CreateCultureEvent>(_onCreateCulture);
  }

  Future<void> _onCreateCulture(
    CreateCultureEvent event,
    Emitter<CultureState> emit,
  ) async {
    emit(const CultureState.loading());

    try {
      // Only essential synchronous operation
      final culture = await _cultureRepository.create(event.request);

      // Emit success state immediately
      emit(CultureState.success(culture));

      // All secondary operations are asynchronous (fire-and-forget)
      _eventBus.publishAndForget(ComponentEvent.cultureCreated(culture));

    } catch (e) {
      emit(CultureState.error(e.toString()));
    }
  }
}

@freezed
class CultureEvent with _$CultureEvent {
  const factory CultureEvent.createCulture(CreateCultureRequest request) = CreateCultureEvent;
  const factory CultureEvent.loadCultures() = LoadCulturesEvent;
  const factory CultureEvent.updateCulture(Culture culture) = UpdateCultureEvent;
}

@freezed
class CultureState with _$CultureState {
  const factory CultureState.initial() = CultureInitial;
  const factory CultureState.loading() = CultureLoading;
  const factory CultureState.success(Culture culture) = CultureSuccess;
  const factory CultureState.error(String message) = CultureError;
}
```

## External APIs

Based on CultureStack's native Android architecture and PRD requirements, the application integrates with several external APIs and services. Since this is a native mobile app rather than a traditional web application, "external APIs" refer to third-party service integrations.

### Google Drive API v3
**Purpose:** Primary cloud storage and synchronization service for user culture data, photos, and recipes.

- **Documentation:** https://developers.google.com/drive/api/v3/reference
- **Base URL(s):** https://www.googleapis.com/drive/v3/, https://www.googleapis.com/upload/drive/v3/
- **Authentication:** OAuth 2.0 with Google Sign-In API, Drive scope permissions
- **Rate Limits:** 1,000 requests per 100 seconds per user, 100 queries per second per user

**Key Endpoints Used:**
- `GET /files` - List user's culture data files in app folder
- `POST /files` - Create new culture data file or folder structure
- `PATCH /files/{fileId}` - Update existing culture records
- `POST /upload/files` - Upload culture photos with multipart encoding
- `GET /files/{fileId}` - Download culture data or photos for sync

**Integration Notes:**
- Uses app-specific folder scope for user privacy
- Implements exponential backoff for rate limiting
- Batch operations for efficient sync of multiple entities
- Conflict resolution based on file modification timestamps

### Google Sign-In API
**Purpose:** User authentication for premium features and Google Drive access verification.

- **Documentation:** https://developers.google.com/identity/sign-in/android
- **Base URL(s):** Integrated Android SDK, no direct HTTP calls
- **Authentication:** OAuth 2.0 flows with Google Play Services integration
- **Rate Limits:** No explicit rate limits, governed by Google Play Services

**Key Endpoints Used:**
- SDK method: `GoogleSignIn.getClient().signIn()` - Initiate sign-in flow
- SDK method: `GoogleSignIn.getLastSignedInAccount()` - Retrieve cached account
- SDK method: `GoogleSignIn.getClient().signOut()` - Sign out current user
- SDK method: `GoogleSignIn.requestPermissions()` - Request additional scopes

**Integration Notes:**
- Integrated with Google Play Services for seamless UX
- Handles scope incremental authorization for Drive access
- Automatic token refresh managed by SDK
- Graceful fallback to guest mode when unavailable

### Google Play Billing Library
**Purpose:** Premium subscription management and in-app purchase verification for freemium model.

- **Documentation:** https://developer.android.com/google/play/billing
- **Base URL(s):** Integrated Android library, communicates with Google Play servers
- **Authentication:** App signing key verification with Google Play Console
- **Rate Limits:** No explicit limits, managed by Google Play infrastructure

**Key Endpoints Used:**
- Library method: `BillingClient.queryPurchasesAsync()` - Verify active subscriptions
- Library method: `BillingClient.launchBillingFlow()` - Initiate subscription purchase
- Library method: `BillingClient.acknowledgePurchase()` - Acknowledge completed purchase
- Library method: `BillingClient.queryProductDetailsAsync()` - Get subscription pricing

**Integration Notes:**
- Server-side receipt verification for security
- Handles subscription restoration across devices
- Manages purchase state persistence for offline scenarios
- Real-time purchase updates via PurchasesUpdatedListener

### Firebase Cloud Messaging (FCM)
**Purpose:** Push notification delivery for culture reminders and sync notifications.

- **Documentation:** https://firebase.google.com/docs/cloud-messaging
- **Base URL(s):** https://fcm.googleapis.com/v1/projects/{project-id}/messages:send
- **Authentication:** Service account key for server-to-FCM communication
- **Rate Limits:** No published limits for downstream messages, reasonable use expected

**Key Endpoints Used:**
- `POST /v1/projects/{project-id}/messages:send` - Send targeted notification
- SDK method: `FirebaseMessaging.getToken()` - Retrieve device FCM token
- SDK method: `FirebaseMessaging.subscribeToTopic()` - Topic-based messaging
- SDK method: `FirebaseMessaging.setAutoInitEnabled()` - Enable/disable FCM

**Integration Notes:**
- Topic-based messaging for broadcast notifications (maintenance, updates)
- Device-specific tokens for personalized culture reminders
- Notification channels for Android 8.0+ compatibility
- Handles token refresh and registration updates

## Regional Constraints and Fallback Strategies

### Regional Availability Analysis
```kotlin
data class RegionalConstraint(
    val region: String,
    val googlePlayServices: ServiceStatus,
    val googleDriveAccess: ServiceStatus,
    val alternativeRequired: Boolean,
    val marketPenetration: Double
)

val regionalConstraints = listOf(
    RegionalConstraint("China", BLOCKED, BLOCKED, true, 0.20),
    RegionalConstraint("Iran", RESTRICTED, RESTRICTED, true, 0.02),
    RegionalConstraint("Russia", PARTIAL, PARTIAL, false, 0.05),
    RegionalConstraint("EU", AVAILABLE, GDPR_COMPLIANT, false, 0.15),
    RegionalConstraint("North America", AVAILABLE, AVAILABLE, false, 0.35),
    RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
)
// Impact: ~22% of potential users may have limited Google Services access
```

### Rate Limiting and Quota Management
```kotlin
class GoogleDriveRateLimiter {
    private val rateLimiter = RateLimiter.create(8.0) // 8 requests per 10 seconds
    private val burstAllowance = Semaphore(20) // Allow 20 burst requests

    suspend fun executeWithLimit(operation: suspend () -> Result<Any>): Result<Any> {
        return if (burstAllowance.tryAcquire()) {
            try {
                rateLimiter.acquire()
                operation()
            } finally {
                burstAllowance.release()
            }
        } else {
            operationQueue.add(DelayedOperation(operation, System.currentTimeMillis() + 60000))
            Result.failure(RateLimitExceeded(Duration.ofMinutes(1)))
        }
    }
}
```

### Constraint-Aware API Client
```kotlin
class ConstraintAwareApiClient {
    private val regionalLimiter = RegionalRateLimiter()
    private val quotaMonitor = ApiQuotaMonitor()

    suspend fun <T> executeApiCall(
        apiCall: suspend () -> T,
        region: String,
        priority: ApiCallPriority
    ): Result<T> {
        // Check regional constraints
        val regionalConstraint = regionalLimiter.getConstraints(region)
        if (regionalConstraint.blocked) {
            return Result.failure(RegionBlockedError(region))
        }

        // Check quota status
        val quotaStatus = quotaMonitor.getCurrentStatus()
        if (quotaStatus.approachingLimit && priority == LOW) {
            return Result.failure(QuotaThresholdError("Deferring low-priority call"))
        }

        // Execute with appropriate rate limiting
        return regionalLimiter.executeWithConstraints(regionalConstraint) {
            apiCall()
        }
    }
}
```

**External API Integration Summary:**
- **Google Services Ecosystem:** Leverages integrated Android SDKs for optimal UX
- **Rate Limiting:** Intelligent backoff and batching with burst allowance for API quotas
- **Regional Adaptation:** Fallback strategies for 22% of users with limited Google Services
- **Error Handling:** Comprehensive error recovery with graceful degradation
- **Privacy Compliance:** User-controlled data collection with GDPR compliance for EU
- **Offline Resilience:** Local fallbacks when external services unavailable
- **Cost Management:** Free tier optimization with scaling constraint awareness

## Core Workflows

Based on the PRD requirements and architectural components, I'll illustrate key system workflows using sequence diagrams that show the interactions between components, external APIs, and error handling paths.

### Culture Creation Workflow

This workflow demonstrates the complete process of creating a new culture, from user input through local storage and cloud synchronization.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant RM as Recipe Management
    participant DB as Database Component
    participant EB as Event Bus
    participant SM as Sync Management
    participant GD as Google Drive API

    User->>UI: Create new culture
    UI->>RM: Search for recipes
    RM->>DB: Query recipes by plant type
    DB-->>RM: Return matching recipes
    RM-->>UI: Recipe suggestions
    User->>UI: Select recipe & enter details
    UI->>CM: CreateCulture request

    CM->>DB: Insert culture record
    DB-->>CM: Culture created (local)
    CM->>EB: Publish CultureCreated event
    CM-->>UI: Success response
    UI-->>User: Culture created confirmation

    EB->>SM: Handle CultureCreated event
    SM->>GD: Upload culture metadata
    alt Sync Success
        GD-->>SM: Upload successful
        SM->>DB: Update sync status
    else Rate Limited
        GD-->>SM: Rate limit error
        SM->>SM: Queue for delayed sync
    else Network Error
        SM->>SM: Add to offline queue
    end
```

### Multi-Device Observation Sync Workflow

This sequence shows the complex interaction when observations are logged on one device and synchronized to another, including conflict resolution.

```mermaid
sequenceDiagram
    participant U1 as User Device A
    participant U2 as User Device B
    participant CM1 as Culture Mgmt A
    participant CM2 as Culture Mgmt B
    participant DB1 as Database A
    participant DB2 as Database B
    participant SM1 as Sync Mgmt A
    participant SM2 as Sync Mgmt B
    participant GD as Google Drive
    participant CR as Conflict Resolution

    U1->>CM1: Add contamination observation
    CM1->>DB1: Insert observation locally
    CM1->>CM1: Update culture status to CONTAMINATED
    CM1->>DB1: Update culture status
    CM1->>SM1: Queue sync operations

    SM1->>GD: Upload observation + culture update
    GD-->>SM1: Sync successful

    Note over U2: Meanwhile on Device B...
    U2->>CM2: View culture timeline
    CM2->>SM2: Check for remote updates
    SM2->>GD: Fetch latest changes
    GD-->>SM2: New observation + status update

    SM2->>DB2: Compare sync versions
    alt No Conflict
        SM2->>DB2: Apply updates
        SM2->>CM2: Notify of changes
        CM2-->>U2: Show updated status
    else Sync Conflict Detected
        SM2->>CR: Create conflict resolution
        CR->>CR: Analyze conflicts
        CR-->>SM2: Manual resolution required
        SM2->>CM2: Present conflict UI
        CM2-->>U2: User chooses resolution
        U2->>CM2: Select resolution strategy
        CM2->>CR: Apply resolution
        CR->>DB2: Update with merged data
        CR->>SM2: Resolution complete
        SM2->>GD: Upload resolved version
    end
```

### Bulk Subculture Creation Workflow

This demonstrates the batch operation pattern for creating multiple subcultures, including progress tracking and error recovery.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant BO as Batch Operation Service
    participant DB as Database Component
    participant SM as Sync Management
    participant NS as Notification Service

    User->>UI: Create 5 subcultures from Culture C001
    UI->>CM: BatchSubculture request
    CM->>BO: Create batch operation
    BO->>DB: Insert BatchOperation record
    BO-->>CM: Batch operation started
    CM-->>UI: Show progress indicator

    loop For each subculture (5 times)
        BO->>CM: Create individual subculture
        CM->>DB: Insert subculture record
        CM->>SM: Queue for sync
        BO->>BO: Increment completed count
        BO->>DB: Update batch progress
        BO->>UI: Progress update

        alt Subculture Creation Fails
            CM-->>BO: Creation error
            BO->>BO: Log error, continue with next
        end
    end

    BO->>DB: Mark batch as completed
    BO->>NS: Schedule reminders for new subcultures
    BO-->>UI: Batch operation complete
    UI-->>User: 5 subcultures created successfully

    Note over SM: Background sync of all new subcultures
    SM->>SM: Process sync queue
    loop For each queued subculture
        SM->>GD: Upload subculture data
        alt Sync Success
            GD-->>SM: Upload successful
        else Sync Failure
            SM->>SM: Retry with exponential backoff
        end
    end
```

## Data Flow Analysis and Optimization

### Critical Data Transformations in Workflows

#### Culture Creation Data Pipeline
```kotlin
// User Input → Domain Entity → Database Entity → Sync Payload
data class CultureDataFlow(
    val userInput: CultureCreationInput,           // Raw form data
    val domainEntity: CultureDomainEntity,         // Business logic applied
    val dbEntity: CultureDbEntity,                 // Persistence format
    val syncPayload: CultureSyncPayload            // Cloud format
)

// Data validation gates at each transformation
class CultureDataValidator {
    suspend fun validateTransformation(
        input: CultureCreationInput,
        output: CultureDomainEntity
    ): ValidationResult {
        val violations = mutableListOf<ValidationViolation>()

        if (input.species.isBlank()) violations.add(ValidationViolation.REQUIRED_FIELD_MISSING)
        if (input.selectedRecipeId != null && !recipeExists(input.selectedRecipeId)) {
            violations.add(ValidationViolation.INVALID_REFERENCE)
        }

        return ValidationResult(violations)
    }
}
```

#### Photo Processing Pipeline with Data Integrity
```kotlin
data class PhotoProcessingPipeline(
    val rawCapture: RawPhotoCapture,      // 8MB JPEG from camera
    val optimizedPhoto: OptimizedPhoto,    // 1.2MB WebP compressed
    val dbEntity: PhotoDbEntity,           // Database reference
    val syncPayload: DriveUploadPayload    // Cloud upload format
)

class PhotoDataIntegrityValidator {
    fun validatePhotoProcessing(pipeline: PhotoProcessingPipeline): IntegrityResult {
        val issues = mutableListOf<IntegrityIssue>()

        // Check file size reduction is reasonable (not corrupted)
        val compressionRatio = pipeline.optimizedPhoto.optimizedSize.toDouble() /
                               pipeline.rawCapture.fileSize
        if (compressionRatio > 0.8 || compressionRatio < 0.05) {
            issues.add(IntegrityIssue.SUSPICIOUS_COMPRESSION_RATIO)
        }

        // Verify file references are consistent
        if (!fileExists(pipeline.optimizedPhoto.optimizedPath)) {
            issues.add(IntegrityIssue.MISSING_OPTIMIZED_FILE)
        }

        return IntegrityResult(issues)
    }
}
```

### Data Flow Monitoring and Metrics
```kotlin
data class DataFlowMetrics(
    val transformationLatency: Duration,    // Time to process data through pipeline
    val dataLossRate: Double,              // Percentage of data lost in transformation
    val errorRate: Double,                 // Errors per transformation
    val throughput: Double,                // Records per second
    val bottleneckStage: PipelineStage     // Identified bottleneck location
)

class WorkflowDataFlowMonitor {
    fun monitorCultureCreationFlow(): Flow<DataFlowMetrics> {
        return combine(
            measureTransformationLatency(),
            measureDataLossRate(),
            measureErrorRate(),
            measureThroughput()
        ) { latency, lossRate, errorRate, throughput ->
            DataFlowMetrics(
                transformationLatency = latency,
                dataLossRate = lossRate,
                errorRate = errorRate,
                throughput = throughput,
                bottleneckStage = identifyBottleneck(latency, throughput)
            )
        }
    }
}
```

**Core Workflow Design Rationale:**
- **Event-Driven Architecture:** Workflows use event bus for loose coupling and better error isolation
- **Offline-First Design:** All workflows can operate without network connectivity, queuing sync operations
- **Graceful Degradation:** Each workflow has fallback paths for service unavailability
- **Progress Visibility:** Batch operations provide real-time progress feedback to users
- **Error Recovery:** Comprehensive error handling with retry logic and user notification
- **Data Integrity:** Validation gates at each transformation point prevent data corruption
- **Performance Monitoring:** Real-time metrics for bottleneck identification and optimization

## Database Schema

Based on the enhanced data models defined earlier, I'll create concrete database schema definitions using the database type selected in the Tech Stack (SQLite with Room).

### Core Entity Tables

#### Cultures Table
```sql
CREATE TABLE cultures (
    id TEXT PRIMARY KEY NOT NULL,
    culture_id TEXT NOT NULL UNIQUE,
    species TEXT NOT NULL,
    explant_type TEXT NOT NULL,
    source_plant_id TEXT,
    initiation_date INTEGER NOT NULL, -- Unix timestamp
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    initial_conditions TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Subcultures Table
```sql
CREATE TABLE subcultures (
    id TEXT PRIMARY KEY NOT NULL,
    subculture_id TEXT NOT NULL UNIQUE,
    parent_culture_id TEXT NOT NULL,
    subculture_date INTEGER NOT NULL,
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    explant_count INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (parent_culture_id) REFERENCES cultures (id),
    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Recipes Table
```sql
CREATE TABLE recipes (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    ingredients TEXT NOT NULL, -- JSON array of Ingredient objects
    preparation_notes TEXT,
    category TEXT,
    plant_types TEXT, -- JSON array of strings
    difficulty_level TEXT NOT NULL DEFAULT 'BEGINNER',
    tags TEXT, -- JSON array of strings
    usage_count INTEGER NOT NULL DEFAULT 0,
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL
);
```

### Enhanced Entity Tables

#### Photos Table
```sql
CREATE TABLE photos (
    id TEXT PRIMARY KEY NOT NULL,
    filename TEXT NOT NULL,
    observation_id TEXT NOT NULL,
    local_path TEXT NOT NULL,
    cloud_path TEXT,
    thumbnail_path TEXT NOT NULL,
    compression_level TEXT NOT NULL,
    upload_status TEXT NOT NULL DEFAULT 'PENDING',
    file_size INTEGER NOT NULL,
    captured_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (observation_id) REFERENCES observations (id)
);
```

#### Sync Queue Table
```sql
CREATE TABLE sync_queue (
    id TEXT PRIMARY KEY NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    operation TEXT NOT NULL, -- CREATE, UPDATE, DELETE
    priority TEXT NOT NULL DEFAULT 'NORMAL',
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    last_attempt INTEGER,
    status TEXT NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    created_at INTEGER NOT NULL,
    scheduled_for INTEGER NOT NULL
);
```

## Query Pattern Analysis and Optimization

### Critical Query Patterns and Performance

#### Culture Timeline Dashboard Query (Most Frequent)
```sql
-- Optimized two-phase loading for better performance
-- Phase 1: Load essential data (target: <20ms)
SELECT c.id, c.culture_id, c.species, c.status, c.updated_at
FROM cultures c
WHERE c.is_deleted = 0 AND c.status IN ('HEALTHY', 'READY_FOR_TRANSFER', 'IN_ROOTING')
ORDER BY c.updated_at DESC
LIMIT 50;

-- Phase 2: Async load details for visible items
SELECT r.name, COUNT(s.id) as subculture_count, MAX(o.observation_date) as last_observation
FROM cultures c
LEFT JOIN recipes r ON c.recipe_id = r.id
LEFT JOIN subcultures s ON c.id = s.parent_culture_id AND s.is_deleted = 0
LEFT JOIN observations o ON c.id = o.culture_id AND o.is_deleted = 0
WHERE c.id = ?
GROUP BY c.id;
```

#### Enhanced Search Performance with FTS
```sql
-- Full-text search index for recipe discovery
CREATE VIRTUAL TABLE recipe_fts USING fts5(
    recipe_id,
    name,
    description,
    plant_types,
    tags,
    ingredients
);

-- Optimized search query (target: <100ms for 1000+ recipes)
SELECT r.id, r.name, r.description, r.difficulty_level, r.usage_count
FROM recipe_fts fts
JOIN recipes r ON fts.recipe_id = r.id
WHERE recipe_fts MATCH 'orchid AND medium'
  AND r.difficulty_level = 'BEGINNER'
  AND r.is_deleted = 0
ORDER BY r.usage_count DESC
LIMIT 20;
```

### Performance Optimization Strategies

#### Materialized Views for Expensive Aggregations
```sql
-- Pre-computed culture summary for dashboard performance
CREATE TABLE culture_summary_cache (
    culture_id TEXT PRIMARY KEY,
    subculture_count INTEGER,
    observation_count INTEGER,
    photo_count INTEGER,
    last_observation_date INTEGER,
    contamination_detected INTEGER,
    cache_updated_at INTEGER,

    FOREIGN KEY (culture_id) REFERENCES cultures (id)
);
```

#### Critical Performance Indexes
```sql
-- Timeline queries (most frequent)
CREATE INDEX idx_culture_timeline
    ON cultures(status, updated_at DESC, is_deleted);

-- Recipe discovery queries
CREATE INDEX idx_recipe_discovery
    ON recipes(plant_types, difficulty_level, usage_count DESC);

-- Observation timeline per culture
CREATE INDEX idx_observation_timeline
    ON observations(culture_id, observation_date DESC);

-- Sync queue processing
CREATE INDEX idx_sync_queue_processing
    ON sync_queue(status, priority, scheduled_for);

-- Photo sync status queries
CREATE INDEX idx_photo_sync
    ON photos(upload_status, created_at);
```

### Query Performance Monitoring
```kotlin
data class QueryPerformanceMetrics(
    val queryType: String,
    val executionTimeMs: Long,
    val recordsScanned: Long,
    val recordsReturned: Long,
    val indexesUsed: List<String>,
    val optimizationRecommendations: List<String>
)

class DatabasePerformanceMonitor {
    fun analyzeSlowQueries(): Flow<QueryPerformanceAlert> {
        return queryExecutionTimes
            .filter { it.executionTime > Duration.ofMillis(100) }
            .map { slowQuery ->
                QueryPerformanceAlert(
                    query = slowQuery.sql,
                    executionTime = slowQuery.executionTime,
                    suggestedOptimizations = generateOptimizations(slowQuery)
                )
            }
    }
}
```

### Critical Performance Targets
- **Culture Timeline Load:** <50ms for 1000 cultures
- **Recipe Search:** <100ms for 1000+ recipes with full-text search
- **Photo Gallery:** <200ms for 50 photos with metadata
- **Sync Conflict Detection:** <50ms for 100 entities
- **Batch Operation Progress:** <25ms per progress update

**Database Schema Design Rationale:**
- **Offline-First:** All tables support offline operation with sync metadata
- **Performance Optimized:** Indexes and materialized views based on actual query patterns
- **Data Integrity:** Foreign key constraints and triggers enforce business rules
- **Sync-Aware:** Every entity includes sync versioning and conflict resolution support
- **Scalable:** Schema supports efficient queries up to 100K+ records per table
- **Query-Optimized:** Full-text search, covering indexes, and cursor-based pagination
- **Cache-Friendly:** Materialized views for expensive aggregations with trigger maintenance

## Coding Standards & Style Guide

### Flutter/Dart Coding Standards

#### **MANDATORY Code Structure (ALWAYS enforce)**
```dart
// File structure (top to bottom):
// 1. imports (dart: first, package: second, relative: last)
// 2. main exported widget
// 3. private subwidgets
// 4. helper functions
// 5. constants/static content
// 6. types/models

// Required imports order example:
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/user.dart';
```

#### **MANDATORY Naming Conventions (ALWAYS enforce)**
- **Classes**: `PascalCase` (e.g., `UserProfileWidget`, `AuthenticationBloc`)
- **Functions/Methods**: `camelCase` starting with verb (e.g., `fetchUser()`, `validateEmail()`)
- **Variables**: `camelCase` with auxiliary verbs (e.g., `isLoading`, `hasError`, `canSubmit`)
- **Files**: `snake_case` (e.g., `user_profile_widget.dart`, `auth_bloc.dart`)
- **Constants**: `lowerCamelCase` (e.g., `defaultPadding`, `maxRetryAttempts`)
- **Enums**: `PascalCase` with `@JsonValue(int)` for database storage

#### **MANDATORY Widget Patterns (ALWAYS enforce)**
```dart
// ALWAYS use const constructors when possible
class MyWidget extends StatelessWidget {
  const MyWidget({super.key, required this.data});
  final String data;

  @override
  Widget build(BuildContext context) =>
    // ALWAYS use arrow syntax for single expressions
    Container(child: Text(data)); // ALWAYS trailing comma
}

// ALWAYS create private widget classes, NEVER widget methods
class _PrivateSubWidget extends StatelessWidget {
  const _PrivateSubWidget({required this.value});
  final int value;
  @override
  Widget build(BuildContext context) => Text('$value');
}
```

#### **MANDATORY State Management (ALWAYS enforce)**
```dart
// Cubit for simple state, Bloc for complex events
class UserCubit extends Cubit<UserState> {
  UserCubit() : super(const UserState.initial());

  Future<void> fetchUser() async {
    emit(const UserState.loading());
    try {
      final user = await _repository.getUser();
      emit(UserState.loaded(user));
    } catch (e) {
      emit(UserState.error(e.toString()));
    }
  }
}

// ALWAYS use Freezed for state classes
@freezed
class UserState with _$UserState {
  const factory UserState.initial() = _Initial;
  const factory UserState.loading() = _Loading;
  const factory UserState.loaded(User user) = _Loaded;
  const factory UserState.error(String message) = _Error;
}
```

#### **MANDATORY Performance Rules (ALWAYS enforce)**
- **ALWAYS** use `const` constructors where possible
- **ALWAYS** use `ListView.builder` for lists, NEVER `ListView(children: [])`
- **ALWAYS** use `AssetImage` for local images, `cached_network_image` for remote
- **ALWAYS** include `errorBuilder` in `Image.network`
- **ALWAYS** prefer `StatelessWidget` over `StatefulWidget`
- **ALWAYS** break deep widget trees into smaller components (max 3-4 levels)

#### **MANDATORY Code Style (ALWAYS enforce)**
- **Line length**: Maximum 80 characters
- **Trailing commas**: ALWAYS add for multi-parameter functions/constructors
- **Arrow syntax**: ALWAYS use for single expressions (`=> expression`)
- **Debugging**: ALWAYS use `log()`, NEVER `print()`
- **Navigation**: ALWAYS use `GoRouter` or `auto_route`, NEVER `Navigator.push`

#### **Code Quality Checklist**
- [ ] All widgets use `const` constructors where possible
- [ ] No `print()` statements (use `log()`)
- [ ] Trailing commas on multi-parameter functions
- [ ] Proper error handling with `SelectableText.rich`
- [ ] State management follows Bloc/Cubit patterns
- [ ] Firebase queries are limited and indexed
- [ ] No deep widget nesting (max 3-4 levels)
- [ ] Build runner executed after model changes
- [ ] Navigation uses GoRouter, not Navigator
- [ ] TextField inputs properly configured

## Security Architecture

### Data Protection Strategy

#### **Encryption at Rest**
```dart
// SQLCipher integration for local database encryption
@DriftDatabase(
  tables: [Cultures, Recipes, Observations],
  daos: [CultureDao, RecipeDao, ObservationDao],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  static LazyDatabase _openConnection() {
    return LazyDatabase(() async {
      final dbFolder = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbFolder.path, 'culturestack_encrypted.db'));

      // Generate or retrieve encryption key
      final encryptionKey = await _getOrCreateEncryptionKey();

      return DatabaseConnection(
        NativeDatabase(
          file,
          setup: (database) {
            // Enable SQLCipher encryption
            database.execute("PRAGMA key = '$encryptionKey'");
            database.execute('PRAGMA cipher_page_size = 4096');
            database.execute('PRAGMA kdf_iter = 64000');
            database.execute('PRAGMA cipher_hmac_algorithm = HMAC_SHA512');
            database.execute('PRAGMA cipher_kdf_algorithm = PBKDF2_HMAC_SHA512');
          },
        ),
      );
    });
  }

  static Future<String> _getOrCreateEncryptionKey() async {
    const storage = FlutterSecureStorage();
    String? key = await storage.read(key: 'db_encryption_key');
    if (key == null) {
      // Generate new 256-bit key
      final random = Random.secure();
      final keyBytes = List<int>.generate(32, (i) => random.nextInt(256));
      key = base64Encode(keyBytes);
      await storage.write(key: 'db_encryption_key', value: key);
    }
    return key;
  }
}
```

#### **Authentication & Authorization**
```dart
class SecureGoogleAuthService {
  final GoogleSignIn _googleSignIn;

  SecureGoogleAuthService()
      : _googleSignIn = GoogleSignIn(
          scopes: [
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/userinfo.email',
          ],
          forceCodeForRefreshToken: true, // Force consent for security
        );

  Future<Result<SecureAuthResult>> signInSecurely() async {
    try {
      final account = await _googleSignIn.signIn();
      if (account == null) {
        return Result.error(AuthCancelledException());
      }

      // Validate token before proceeding
      final auth = await account.authentication;
      final isValid = await _validateToken(auth.accessToken, auth.idToken);

      if (!isValid) {
        await _googleSignIn.signOut();
        return Result.error(InvalidTokenException());
      }

      await _storeSecureSession(account, auth);
      return Result.success(SecureAuthResult(account: account));

    } catch (e) {
      return Result.error(AuthException(e.toString()));
    }
  }
}
```

### Privacy Protection

#### **PII Handling**
```dart
class PrivacyProtectionService {
  // Never log or store personally identifiable information
  static void logSecurely(String message, {Map<String, dynamic>? metadata}) {
    final sanitizedMetadata = metadata?.map((key, value) {
      if (_isSensitiveField(key)) {
        return MapEntry(key, '[REDACTED]');
      }
      return MapEntry(key, value);
    });

    log(message, metadata: sanitizedMetadata);
  }

  static bool _isSensitiveField(String field) {
    final sensitiveFields = {
      'email', 'user_id', 'access_token', 'refresh_token',
      'device_id', 'ip_address', 'location'
    };
    return sensitiveFields.contains(field.toLowerCase());
  }
}
```

## Deployment Architecture

### Build & Release Pipeline

#### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: CultureStack Deployment Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
      - name: Install dependencies
        run: flutter pub get
      - name: Run code generation
        run: flutter pub run build_runner build --delete-conflicting-outputs
      - name: Analyze code
        run: flutter analyze --fatal-infos
      - name: Run unit tests
        run: flutter test --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

  build-android:
    needs: [test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
      - name: Setup Android signing
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE }}" | base64 -d > android/app/keystore.jks
          echo "storeFile=keystore.jks" >> android/key.properties
          echo "storePassword=${{ secrets.KEYSTORE_PASSWORD }}" >> android/key.properties
      - name: Build App Bundle
        run: flutter build appbundle --release
      - name: Upload to Google Play Console
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.culturestack.app
          releaseFiles: build/app/outputs/bundle/release/app-release.aab
          track: internal
```

## Performance Architecture

### Database Performance Optimization

#### **Query Optimization Strategy**
```dart
class OptimizedCultureDao extends DatabaseAccessor<AppDatabase> {
  // Paginated queries for large datasets
  Future<List<CultureData>> getCulturesPage(int offset, int limit) {
    return (select(cultures)
          ..where((c) => c.isDeleted.equals(false))
          ..orderBy([(c) => OrderingTerm.desc(c.updatedAt)])
          ..limit(limit, offset: offset))
        .get();
  }

  // Efficient search with indexes
  Future<List<CultureData>> searchOptimized(String query) {
    return customSelect('''
      SELECT * FROM cultures
      WHERE isDeleted = 0
        AND (species LIKE ? OR cultureId LIKE ?)
      ORDER BY
        CASE WHEN species LIKE ? THEN 1 ELSE 2 END,
        updatedAt DESC
      LIMIT 50
    ''', variables: [
      Variable.withString('%$query%'),
      Variable.withString('%$query%'),
      Variable.withString('$query%'), // Exact match priority
    ]).map((row) => CultureData.fromJson(row.data)).get();
  }
}
```

### Memory Management
```dart
class MemoryOptimizedImageService {
  final LRUMap<String, Uint8List> _imageCache = LRUMap(maxSize: 50);

  Future<Widget> buildOptimizedImage(String photoPath) async {
    return FutureBuilder<Uint8List?>(
      future: _loadImageOptimized(photoPath),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            cacheWidth: 300, // Limit memory usage
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.broken_image),
          );
        }
        return CircularProgressIndicator();
      },
    );
  }
}
```

## Component Architecture

### Feature-Based Architecture

#### **Core Components Structure**
```
lib/
├── core/                     # Shared core functionality
│   ├── database/             # Drift database setup
│   ├── networking/           # HTTP clients, API services
│   ├── storage/              # Local storage, secure storage
│   ├── error/                # Error handling, exceptions
│   └── utils/                # Utilities, extensions
├── features/                 # Feature modules
│   ├── auth/                 # Authentication feature
│   │   ├── data/             # Data layer (repositories, data sources)
│   │   ├── domain/           # Domain layer (entities, use cases)
│   │   └── presentation/     # Presentation layer (widgets, blocs)
│   ├── cultures/             # Culture management feature
│   ├── recipes/              # Recipe management feature
│   ├── observations/         # Observation tracking feature
│   └── sync/                 # Data synchronization feature
└── shared/                   # Shared UI components, themes
    ├── widgets/              # Reusable widgets
    ├── themes/               # App themes, colors
    └── navigation/           # Route configuration
```

#### **Feature Module Template**
```dart
// features/cultures/domain/entities/culture.dart
@freezed
class Culture with _$Culture {
  const factory Culture({
    required String id,
    required String cultureId,
    required String species,
    // ... other properties
  }) = _Culture;
}

// features/cultures/domain/repositories/culture_repository.dart
abstract class CultureRepository {
  Future<Result<List<Culture>>> getAllCultures();
  Future<Result<Culture>> getCultureById(String id);
  Future<Result<Culture>> createCulture(Culture culture);
  Future<Result<Culture>> updateCulture(Culture culture);
  Future<Result<void>> deleteCulture(String id);
}

// features/cultures/presentation/bloc/culture_bloc.dart
class CultureBloc extends Bloc<CultureEvent, CultureState> {
  final CultureRepository _repository;

  CultureBloc(this._repository) : super(const CultureState.initial()) {
    on<LoadCultures>(_onLoadCultures);
    on<CreateCulture>(_onCreateCulture);
    on<UpdateCulture>(_onUpdateCulture);
  }
}
```

## Core Workflows

### Primary User Workflows

#### **Workflow 1: Create New Culture**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as CultureFormWidget
    participant B as CultureBloc
    participant R as CultureRepository
    participant DB as LocalDatabase
    participant S as SyncService

    U->>UI: Tap "Add Culture" FAB
    UI->>UI: Navigate to CultureFormPage
    U->>UI: Fill form (species, explant, medium)
    U->>UI: Tap "Create Culture"
    UI->>B: CreateCultureEvent
    B->>B: Validate form data
    B->>R: createCulture(Culture)
    R->>DB: Insert into cultures table
    DB-->>R: Success with generated ID
    R->>S: Queue sync operation
    S-->>R: Queued successfully
    R-->>B: Result<Culture>
    B->>B: Emit CultureState.created(culture)
    UI->>UI: Show success message
    UI->>UI: Navigate to culture detail
```

#### **Workflow 2: Add Observation with Photo**
```mermaid
sequenceDiagram
    participant U as User
    participant UI as ObservationWidget
    participant C as Camera
    participant B as ObservationBloc
    participant P as PhotoService
    participant DB as Database
    participant S as SyncService

    U->>UI: Open culture detail
    U->>UI: Tap "Add Observation"
    UI->>UI: Open observation form
    U->>UI: Tap camera button
    UI->>C: Launch camera intent
    C-->>UI: Return photo data
    UI->>P: Store photo locally
    P->>P: Compress and encrypt photo
    P-->>UI: Return photo path
    U->>UI: Fill observation details
    U->>UI: Tap "Save"
    UI->>B: AddObservationEvent
    B->>DB: Insert observation + photo reference
    DB-->>B: Success
    B->>S: Queue photo for sync
    S-->>B: Queued
    B->>B: Emit ObservationState.added
    UI->>UI: Show in timeline
```

#### **Workflow 3: Background Sync Process**
```mermaid
sequenceDiagram
    participant W as WorkManager
    participant S as SyncService
    participant Q as SyncQueue
    participant G as GoogleDriveAPI
    participant D as Database

    W->>S: Trigger periodic sync
    S->>S: Check network connectivity
    S->>S: Check Google Services availability
    S->>Q: Get pending operations
    Q-->>S: List of sync operations
    S->>S: Sort by priority (High→Normal→Low)

    loop For each operation
        S->>S: Check rate limits
        alt Rate limit OK
            S->>G: Execute API call
            G-->>S: Success/Failure response
            alt Success
                S->>D: Update local sync metadata
                S->>Q: Mark operation complete
            else Failure
                S->>Q: Increment retry count
                S->>S: Schedule retry with backoff
            end
        else Rate limit hit
            S->>S: Schedule delayed retry
            break
        end
    end

    S->>S: Update sync status
    S->>W: Schedule next sync window
```

#### **Workflow 4: Conflict Resolution**
```mermaid
sequenceDiagram
    participant S as SyncService
    participant D as Database
    participant G as GoogleDrive
    participant U as User
    participant UI as ConflictResolutionWidget

    S->>G: Fetch remote data
    G-->>S: Remote entity data
    S->>D: Get local entity data
    D-->>S: Local entity data
    S->>S: Detect conflict (different versions)
    S->>D: Store conflict record
    S->>UI: Show conflict notification

    U->>UI: Tap conflict notification
    UI->>UI: Show conflict resolution dialog
    UI->>UI: Display both versions side-by-side

    alt User chooses local version
        U->>UI: Select "Keep Local"
        UI->>S: ResolveConflict(KeepLocal)
        S->>G: Upload local version
        S->>D: Update sync metadata
    else User chooses remote version
        U->>UI: Select "Keep Remote"
        UI->>S: ResolveConflict(KeepRemote)
        S->>D: Update local with remote data
        S->>D: Update sync metadata
    else User chooses merge
        U->>UI: Select "Merge Changes"
        UI->>UI: Show merge interface
        U->>UI: Manually merge conflicts
        UI->>S: ResolveConflict(Merged)
        S->>D: Save merged entity
        S->>G: Upload merged version
    end

    S->>D: Mark conflict resolved
    UI->>UI: Show success message
```

### Critical Success Metrics
- **Culture Creation Time**: <30 seconds from form open to save
- **Photo Capture & Save**: <5 seconds from camera to storage
- **Timeline Load**: <2 seconds for 100 cultures with photos
- **Sync Success Rate**: >95% for normal network conditions
- **Conflict Resolution**: <60 seconds average user decision time
- **Offline Capability**: 100% functionality without network access