import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/culture_list_cubit.dart';
import '../data/database/database.dart';
import '../data/models/culture_status.dart';
import 'culture_detail_screen.dart';

class TimelineScreen extends StatelessWidget {
  const TimelineScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CultureListCubit(CultureDatabase())..loadCultures(),
      child: const _TimelineView(),
    );
  }
}

class _TimelineView extends StatelessWidget {
  const _TimelineView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CultureStack'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      body: BlocBuilder<CultureListCubit, CultureListState>(
        builder: (context, state) => state.when(
          initial: () => const Center(child: CircularProgressIndicator()),
          loading: () => const Center(child: CircularProgressIndicator()),
          loaded: (items) => items.isEmpty
              ? const _EmptyTimelineWidget()
              : _TimelineWidget(items: items),
          error: (message) => _ErrorWidget(message: message),
        ),
      ),
    );
  }
}

class _EmptyTimelineWidget extends StatelessWidget {
  const _EmptyTimelineWidget();

  @override
  Widget build(BuildContext context) => Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.science,
                size: 120,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 32),
              Text(
                'Welcome to CultureStack',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Your Plant Tissue Culture Management App',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.7),
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.add_circle_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No Cultures Yet',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Start by creating your first culture using the Add tab below.',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
}

class _TimelineWidget extends StatelessWidget {
  const _TimelineWidget({required this.items});

  final List<TimelineItem> items;

  @override
  Widget build(BuildContext context) => RefreshIndicator(
        onRefresh: () => context.read<CultureListCubit>().refreshCultures(),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: items.length,
          itemBuilder: (context, index) => items[index].when(
            culture: (culture) => _CultureCard(culture: culture),
            subculture: (subculture, parentCultureId) => _SubcultureCard(
              subculture: subculture,
              parentCultureId: parentCultureId,
            ),
          ),
        ),
      );
}

class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.message});

  final String message;

  @override
  Widget build(BuildContext context) => Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SelectableText.rich(
            TextSpan(
              text: 'Error: $message',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
}

class _CultureCard extends StatelessWidget {
  const _CultureCard({required this.culture});

  final Culture culture;

  @override
  Widget build(BuildContext context) => Card(
        margin: const EdgeInsets.only(bottom: 12.0),
        child: InkWell(
          onTap: () => _navigateToCultureDetail(context, culture),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        culture.cultureId,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ),
                    _StatusIndicator(status: culture.status),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  culture.species,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const SizedBox(height: 4),
                Text(
                  'Explant: ${culture.explantType}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  'Initiated: ${_formatDate(culture.initiationDate)} (${_daysSinceInitiation(culture.initiationDate)} days ago)',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ),
      );

  void _navigateToCultureDetail(BuildContext context, Culture culture) {
    Navigator.of(context).push(
      MaterialPageRoute<void>(
        builder: (context) => CultureDetailScreen(cultureId: culture.id),
      ),
    );
  }

  String _formatDate(DateTime date) => '${date.day}/${date.month}/${date.year}';

  int _daysSinceInitiation(DateTime initiationDate) =>
      DateTime.now().difference(initiationDate).inDays;
}

class _StatusIndicator extends StatelessWidget {
  const _StatusIndicator({required this.status});

  final CultureStatus status;

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getStatusColor(context),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status.displayName,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: _getTextColor(context),
                fontWeight: FontWeight.w500,
              ),
        ),
      );

  Color _getStatusColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (status) {
      case CultureStatus.healthy:
        return colorScheme.primaryContainer;
      case CultureStatus.contaminated:
        return colorScheme.errorContainer;
      case CultureStatus.readyForTransfer:
      case CultureStatus.inRooting:
      case CultureStatus.acclimatizing:
        return colorScheme.tertiaryContainer;
      case CultureStatus.completed:
      case CultureStatus.disposed:
        return colorScheme.surfaceContainerHighest;
    }
  }

  Color _getTextColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (status) {
      case CultureStatus.healthy:
        return colorScheme.onPrimaryContainer;
      case CultureStatus.contaminated:
        return colorScheme.onErrorContainer;
      case CultureStatus.readyForTransfer:
      case CultureStatus.inRooting:
      case CultureStatus.acclimatizing:
        return colorScheme.onTertiaryContainer;
      case CultureStatus.completed:
      case CultureStatus.disposed:
        return colorScheme.onSurfaceVariant;
    }
  }
}

class _SubcultureCard extends StatelessWidget {
  const _SubcultureCard({
    required this.subculture,
    required this.parentCultureId,
  });

  final Subculture subculture;
  final String parentCultureId;

  @override
  Widget build(BuildContext context) => Card(
        margin: const EdgeInsets.only(bottom: 12.0, left: 24.0),
        child: InkWell(
          onTap: () => _navigateToSubcultureDetail(context, subculture),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_tree,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        subculture.subcultureId,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ),
                    _StatusIndicator(status: subculture.status),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'From: $parentCultureId',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Explants: ${subculture.explantCount}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  'Created: ${_formatDate(subculture.subcultureDate)} (${_daysSinceCreation(subculture.subcultureDate)} days ago)',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ),
      );

  void _navigateToSubcultureDetail(
      BuildContext context, Subculture subculture) {
    // TODO: Navigate to subculture detail screen when implemented
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Subculture detail view for ${subculture.subcultureId} - Coming soon!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _formatDate(DateTime date) => '${date.day}/${date.month}/${date.year}';

  int _daysSinceCreation(DateTime creationDate) =>
      DateTime.now().difference(creationDate).inDays;
}
