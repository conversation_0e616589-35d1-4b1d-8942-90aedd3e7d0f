import 'package:flutter_test/flutter_test.dart';
import 'package:culture_stack/data/models/culture_status.dart';

void main() {
  group('CultureStatus', () {
    test('should have correct values', () {
      expect(CultureStatus.healthy.value, 0);
      expect(CultureStatus.contaminated.value, 1);
      expect(CultureStatus.readyForTransfer.value, 2);
      expect(CultureStatus.inRooting.value, 3);
      expect(CultureStatus.acclimatizing.value, 4);
      expect(CultureStatus.completed.value, 5);
      expect(CultureStatus.disposed.value, 6);
    });

    test('should have correct display names', () {
      expect(CultureStatus.healthy.displayName, 'Healthy');
      expect(CultureStatus.contaminated.displayName, 'Contaminated');
      expect(CultureStatus.readyForTransfer.displayName, 'Ready for Transfer');
      expect(CultureStatus.inRooting.displayName, 'In Rooting');
      expect(CultureStatus.acclimatizing.displayName, 'Acclimatizing');
      expect(CultureStatus.completed.displayName, 'Completed');
      expect(CultureStatus.disposed.displayName, 'Disposed');
    });

    test('fromValue should return correct status', () {
      expect(CultureStatus.fromValue(0), CultureStatus.healthy);
      expect(CultureStatus.fromValue(1), CultureStatus.contaminated);
      expect(CultureStatus.fromValue(2), CultureStatus.readyForTransfer);
      expect(CultureStatus.fromValue(3), CultureStatus.inRooting);
      expect(CultureStatus.fromValue(4), CultureStatus.acclimatizing);
      expect(CultureStatus.fromValue(5), CultureStatus.completed);
      expect(CultureStatus.fromValue(6), CultureStatus.disposed);
    });

    test('fromValue should return healthy for invalid value', () {
      expect(CultureStatus.fromValue(-1), CultureStatus.healthy);
      expect(CultureStatus.fromValue(999), CultureStatus.healthy);
    });
  });
}
