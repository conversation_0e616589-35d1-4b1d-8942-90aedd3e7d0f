import 'package:drift/drift.dart';
import 'package:json_annotation/json_annotation.dart';
import 'culture_status.dart';

part 'subculture.g.dart';

/// Subculture entity representing a subculture record
@JsonSerializable()
class Subculture {
  const Subculture({
    required this.id,
    required this.subcultureId,
    required this.parentCultureId,
    required this.subcultureDate,
    required this.mediumComposition,
    required this.explantCount,
    required this.status,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.syncedAt,
    this.isDeleted = false,
  });

  /// Unique identifier (UUID)
  final String id;

  /// Human-readable subculture identifier
  final String subcultureId;

  /// Parent culture identifier
  final String parentCultureId;

  /// Date when subculture was performed
  final DateTime subcultureDate;

  /// Medium composition for this subculture
  final String mediumComposition;

  /// Number of explants in this subculture
  final int explantCount;

  /// Current status of the subculture
  final CultureStatus status;

  /// Additional notes (optional)
  final String? notes;

  /// Record creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last sync timestamp (optional)
  final DateTime? syncedAt;

  /// Soft delete flag
  final bool isDeleted;

  factory Subculture.fromJson(Map<String, dynamic> json) => _$SubcultureFromJson(json);
  Map<String, dynamic> toJson() => _$SubcultureToJson(this);

  Subculture copyWith({
    String? id,
    String? subcultureId,
    String? parentCultureId,
    DateTime? subcultureDate,
    String? mediumComposition,
    int? explantCount,
    CultureStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? syncedAt,
    bool? isDeleted,
  }) {
    return Subculture(
      id: id ?? this.id,
      subcultureId: subcultureId ?? this.subcultureId,
      parentCultureId: parentCultureId ?? this.parentCultureId,
      subcultureDate: subcultureDate ?? this.subcultureDate,
      mediumComposition: mediumComposition ?? this.mediumComposition,
      explantCount: explantCount ?? this.explantCount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
