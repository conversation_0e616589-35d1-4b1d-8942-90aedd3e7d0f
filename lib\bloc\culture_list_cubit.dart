import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../data/database/database.dart';

part 'culture_list_cubit.freezed.dart';
part 'culture_list_state.dart';

class CultureListCubit extends Cubit<CultureListState> {
  CultureListCubit(this._database) : super(const CultureListState.initial());

  final CultureDatabase _database;
  StreamSubscription<List<Culture>>? _culturesSubscription;

  void loadCultures() {
    emit(const CultureListState.loading());

    _culturesSubscription?.cancel();
    _culturesSubscription = _database.watchAllCultures().listen(
      (cultures) {
        final activeCultures = cultures
            .where((c) => !c.isDeleted)
            .toList()
          ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

        emit(CultureListState.loaded(activeCultures));
      },
      onError: (error) {
        emit(CultureListState.error(error.toString()));
      },
    );
  }

  Future<void> refreshCultures() async {
    try {
      final cultures = await _database.getAllCultures();
      final activeCultures = cultures
          .where((c) => !c.isDeleted)
          .toList()
        ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      emit(CultureListState.loaded(activeCultures));
    } catch (error) {
      emit(CultureListState.error(error.toString()));
    }
  }

  @override
  Future<void> close() {
    _culturesSubscription?.cancel();
    return super.close();
  }
}