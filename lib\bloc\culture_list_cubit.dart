import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../data/database/database.dart';

part 'culture_list_cubit.freezed.dart';
part 'culture_list_state.dart';

// Timeline item wrapper for cultures and subcultures
@freezed
class TimelineItem with _$TimelineItem {
  const factory TimelineItem.culture(Culture culture) = _CultureItem;
  const factory TimelineItem.subculture(
      Subculture subculture, String parentCultureId) = _SubcultureItem;
}

class CultureListCubit extends Cubit<CultureListState> {
  CultureListCubit(this._database) : super(const CultureListState.initial());

  final CultureDatabase _database;
  StreamSubscription<List<Culture>>? _culturesSubscription;
  StreamSubscription<List<Subculture>>? _subculturesSubscription;

  void loadCultures() {
    emit(const CultureListState.loading());
    _loadTimelineItems();
  }

  void _loadTimelineItems() {
    _culturesSubscription?.cancel();
    _subculturesSubscription?.cancel();

    // Watch cultures and subcultures separately, then combine
    _culturesSubscription = _database.watchAllCultures().listen(
      (_) => _refreshTimelineItems(),
      onError: (error) {
        emit(CultureListState.error(error.toString()));
      },
    );

    // Also watch for subculture changes
    _subculturesSubscription = Stream.periodic(const Duration(seconds: 2))
        .asyncMap((_) => _database.getAllSubcultures())
        .listen(
      (_) => _refreshTimelineItems(),
      onError: (error) {
        emit(CultureListState.error(error.toString()));
      },
    );
  }

  Future<void> _refreshTimelineItems() async {
    try {
      final cultures = await _database.getAllCultures();
      final subcultures = await _database.getAllSubcultures();

      final timelineItems = <TimelineItem>[];

      // Add cultures as timeline items
      for (final culture in cultures.where((c) => !c.isDeleted)) {
        timelineItems.add(TimelineItem.culture(culture));
      }

      // Add subcultures as timeline items
      for (final subculture in subcultures.where((s) => !s.isDeleted)) {
        timelineItems.add(
            TimelineItem.subculture(subculture, subculture.parentCultureId));
      }

      // Sort by date (cultures by updatedAt, subcultures by subcultureDate)
      timelineItems.sort((a, b) {
        final dateA = a.when(
          culture: (culture) => culture.updatedAt,
          subculture: (subculture, _) => subculture.subcultureDate,
        );
        final dateB = b.when(
          culture: (culture) => culture.updatedAt,
          subculture: (subculture, _) => subculture.subcultureDate,
        );
        return dateB.compareTo(dateA); // Most recent first
      });

      emit(CultureListState.loaded(timelineItems));
    } catch (error) {
      emit(CultureListState.error(error.toString()));
    }
  }

  Future<void> refreshCultures() async {
    await _refreshTimelineItems();
  }

  @override
  Future<void> close() {
    _culturesSubscription?.cancel();
    _subculturesSubscription?.cancel();
    return super.close();
  }
}
