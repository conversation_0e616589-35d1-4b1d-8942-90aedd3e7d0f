import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/screens/add_culture_screen.dart';

void main() {
  group('AddCultureScreen', () {
    testWidgets('displays form fields correctly', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: AddCultureScreen(),
        ),
      );

      // Assert
      expect(find.text('Add Culture'), findsOneWidget);
      expect(find.text('Species/Variety *'), findsOneWidget);
      expect(find.text('Explant Type *'), findsOneWidget);
      expect(find.text('Source Plant ID'), findsOneWidget);
      expect(find.text('Initiation Date *'), findsOneWidget);
      expect(find.text('Medium Composition'), findsOneWidget);
      expect(find.text('Initial Conditions'), findsOneWidget);
      expect(find.text('Save Culture'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('shows validation errors for required fields', (tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: AddCultureScreen(),
        ),
      );

      // Act - Try to save without filling required fields
      await tester.ensureVisible(find.text('Save Culture'));
      await tester.tap(find.text('Save Culture'));
      await tester.pumpAndSettle();

      // Assert - Check that validation errors appear
      expect(find.text('Species/Variety is required'), findsOneWidget);
      expect(find.text('Explant Type is required'), findsOneWidget);
    });

    testWidgets('accepts valid input in form fields', (tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: AddCultureScreen(),
        ),
      );

      // Act - Fill in the form fields
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Species/Variety *'),
        'Orchid Test',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Explant Type *'),
        'Leaf',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Source Plant ID'),
        'P001',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Medium Composition'),
        'MS Medium',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Initial Conditions'),
        'Sterile conditions',
      );

      await tester.pump();

      // Assert - Check that text was entered
      expect(find.text('Orchid Test'), findsOneWidget);
      expect(find.text('Leaf'), findsOneWidget);
      expect(find.text('P001'), findsOneWidget);
      expect(find.text('MS Medium'), findsOneWidget);
      expect(find.text('Sterile conditions'), findsOneWidget);
    });

    testWidgets('date picker opens when initiation date field is tapped', (tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: AddCultureScreen(),
        ),
      );

      // Act - Find the date field by its decoration and tap it
      final dateField = find.byWidgetPredicate(
        (widget) => widget is InkWell &&
                    widget.child is InputDecorator,
      );
      await tester.ensureVisible(dateField);
      await tester.tap(dateField);
      await tester.pumpAndSettle();

      // Assert - Date picker should be visible
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('cancel button navigates back', (tester) async {
      // Arrange
      bool navigatedBack = false;
      await tester.pumpWidget(
        MaterialApp(
          home: Navigator(
            onGenerateRoute: (settings) {
              if (settings.name == '/') {
                return MaterialPageRoute(
                  builder: (context) => Scaffold(
                    body: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const AddCultureScreen(),
                          ),
                        );
                      },
                      child: const Text('Go to Add Culture'),
                    ),
                  ),
                );
              }
              return null;
            },
          ),
        ),
      );

      // Navigate to AddCultureScreen
      await tester.tap(find.text('Go to Add Culture'));
      await tester.pumpAndSettle();

      // Act - Tap cancel button
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Should be back to the original screen
      expect(find.text('Go to Add Culture'), findsOneWidget);
      expect(find.text('Add Culture'), findsNothing);
    });
  });
}
