import 'package:flutter_test/flutter_test.dart';
import 'package:drift/native.dart';

import '../../../lib/data/database/database.dart';
import '../../../lib/data/models/culture_status.dart';

void main() {
  group('Culture ID Generation', () {
    late CultureDatabase database;

    setUp(() {
      // Use in-memory database for testing
      database = CultureDatabase.forTesting(NativeDatabase.memory());
    });

    tearDown(() async {
      await database.close();
    });

    test('generates C001 for first culture', () async {
      // Arrange - Empty database
      final cultures = await database.getAllCultures();
      expect(cultures, isEmpty);

      // Act - Generate first culture ID
      final cultureId = await _generateCultureId(database);

      // Assert
      expect(cultureId, equals('C001'));
    });

    test('generates sequential IDs correctly', () async {
      // Arrange - Add some existing cultures
      final now = DateTime.now();
      await database.insertCulture(CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species 1',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      ));

      await database.insertCulture(CulturesCompanion.insert(
        id: 'test-2',
        cultureId: 'C003', // Skip C002 to test max finding
        species: 'Test Species 2',
        explantType: 'Node',
        initiationDate: now,
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      ));

      // Act
      final nextId = await _generateCultureId(database);

      // Assert - Should be C004 (max + 1)
      expect(nextId, equals('C004'));
    });

    test('handles non-standard culture IDs gracefully', () async {
      // Arrange - Add cultures with non-standard IDs
      final now = DateTime.now();
      await database.insertCulture(CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'CUSTOM001', // Non-standard format
        species: 'Test Species 1',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      ));

      await database.insertCulture(CulturesCompanion.insert(
        id: 'test-2',
        cultureId: 'C005', // Standard format
        species: 'Test Species 2',
        explantType: 'Node',
        initiationDate: now,
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      ));

      // Act
      final nextId = await _generateCultureId(database);

      // Assert - Should be C006 (ignores non-standard format)
      expect(nextId, equals('C006'));
    });

    test('pads numbers correctly', () async {
      // Arrange - Add cultures up to C099
      final now = DateTime.now();
      await database.insertCulture(CulturesCompanion.insert(
        id: 'test-99',
        cultureId: 'C099',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      ));

      // Act
      final nextId = await _generateCultureId(database);

      // Assert - Should be C100 (no padding needed for 3+ digits)
      expect(nextId, equals('C100'));
    });
  });
}

// Helper function that mimics the private method in AddCultureScreen
Future<String> _generateCultureId(CultureDatabase database) async {
  final cultures = await database.getAllCultures();
  
  int maxNumber = 0;
  for (final culture in cultures) {
    final match = RegExp(r'C(\d+)').firstMatch(culture.cultureId);
    if (match != null) {
      final number = int.tryParse(match.group(1)!) ?? 0;
      if (number > maxNumber) {
        maxNumber = number;
      }
    }
  }
  
  final nextNumber = maxNumber + 1;
  return 'C${nextNumber.toString().padLeft(3, '0')}';
}
