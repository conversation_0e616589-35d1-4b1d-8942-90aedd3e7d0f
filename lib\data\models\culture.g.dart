// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'culture.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Culture _$CultureFromJson(Map<String, dynamic> json) => Culture(
      id: json['id'] as String,
      cultureId: json['cultureId'] as String,
      species: json['species'] as String,
      explantType: json['explantType'] as String,
      sourcePlantId: json['sourcePlantId'] as String?,
      initiationDate: DateTime.parse(json['initiationDate'] as String),
      mediumComposition: json['mediumComposition'] as String,
      recipeId: json['recipeId'] as String?,
      initialConditions: json['initialConditions'] as String?,
      status: $enumDecode(_$CultureStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      syncedAt: json['syncedAt'] == null
          ? null
          : DateTime.parse(json['syncedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$CultureToJson(Culture instance) => <String, dynamic>{
      'id': instance.id,
      'cultureId': instance.cultureId,
      'species': instance.species,
      'explantType': instance.explantType,
      'sourcePlantId': instance.sourcePlantId,
      'initiationDate': instance.initiationDate.toIso8601String(),
      'mediumComposition': instance.mediumComposition,
      'recipeId': instance.recipeId,
      'initialConditions': instance.initialConditions,
      'status': _$CultureStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'syncedAt': instance.syncedAt?.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$CultureStatusEnumMap = {
  CultureStatus.healthy: 0,
  CultureStatus.contaminated: 1,
  CultureStatus.readyForTransfer: 2,
  CultureStatus.inRooting: 3,
  CultureStatus.acclimatizing: 4,
  CultureStatus.completed: 5,
  CultureStatus.disposed: 6,
};
