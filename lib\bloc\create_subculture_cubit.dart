import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uuid/uuid.dart';

import '../data/database/database.dart';
import '../data/models/culture_status.dart';

part 'create_subculture_cubit.freezed.dart';
part 'create_subculture_state.dart';

class CreateSubcultureCubit extends Cubit<CreateSubcultureState> {
  CreateSubcultureCubit(this._database) : super(const CreateSubcultureState.initial());

  final CultureDatabase _database;
  final _uuid = const Uuid();

  Future<void> createSubculture({
    required String parentCultureId,
    required DateTime subcultureDate,
    required String mediumComposition,
    required int explantCount,
  }) async {
    emit(const CreateSubcultureState.loading());

    try {
      // Generate unique subculture ID
      final subcultureId = await _database.generateSubcultureId(parentCultureId);
      
      // Create subculture companion for database insertion
      final now = DateTime.now();
      final subculture = SubculturesCompanion.insert(
        id: _uuid.v4(),
        subcultureId: subcultureId,
        parentCultureId: parentCultureId,
        subcultureDate: subcultureDate,
        mediumComposition: mediumComposition,
        explantCount: explantCount,
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );

      // Insert into database
      await _database.insertSubculture(subculture);

      log('Subculture $subcultureId created successfully for parent $parentCultureId', 
          name: 'CreateSubcultureCubit');

      emit(const CreateSubcultureState.success());
    } catch (error) {
      log('Error creating subculture: $error', name: 'CreateSubcultureCubit');
      emit(CreateSubcultureState.error(error.toString()));
    }
  }
}
