# High Level Architecture

### Technical Summary
CultureStack employs a **Flutter cross-platform architecture** with offline-first design, utilizing local SQLite storage synchronized with Google Drive for cloud backup. The application integrates Google Services (Authentication, Drive API, Play Billing) through platform channels to provide seamless user experience while maintaining offline functionality. The architecture prioritizes data integrity and user experience in laboratory environments, supporting the PRD's goal of reducing culture failure rates through reliable mobile access to tracking capabilities across Android and iOS platforms.

### Platform and Infrastructure Choice

**Analysis of Platform Options:**

1. **Google Cloud + Flutter (Recommended)**
   - **Pros:** Cross-platform development, Google Services integration via platform channels, seamless Play Store billing, Drive API optimization, single codebase for Android/iOS
   - **Cons:** Platform channel complexity for native features, Google Services dependency
   - **Rationale:** Aligns perfectly with PRD requirements for Google Drive sync and Play Billing while enabling future iOS support

2. **AWS + Flutter**
   - **Pros:** Enterprise scalability, diverse service ecosystem, cross-platform development
   - **Cons:** Additional complexity for Drive integration, separate billing system needed
   - **Rationale:** Overengineered for mobile-first application

3. **Firebase + Flutter**
   - **Pros:** Real-time sync, integrated authentication, mobile-optimized, excellent Flutter integration
   - **Cons:** Conflicts with Google Drive requirement, different data model
   - **Rationale:** Would require PRD changes for sync mechanism

**Recommendation:** **Google Cloud Platform + Flutter**

**Platform:** Google Cloud Platform
**Key Services:** Google Drive API v3, Google Sign-In API, Google Play Billing Library, Flutter Workmanager
**Deployment Host and Regions:** Google Play Store distribution (Android), App Store distribution (iOS future), user data stored in personal Google Drive

### Repository Structure

**Structure:** Monorepo with Flutter-focused organization
**Monorepo Tool:** Flutter package structure with pub dependencies
**Package Organization:** Feature-based packages (culture, recipes, sync, auth) with shared core package following Flutter conventions

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Device"
        A[Flutter App]
        B[SQLite Database]
        C[Local File Storage]
        D[Platform Channels]
        A --> B
        A --> C
        A --> D
    end

    subgraph "Google Services"
        E[Google Drive API]
        F[Google Sign-In]
        G[Play Billing]
    end

    subgraph "User's Google Account"
        H[Personal Google Drive]
        I[Play Store Account]
    end

    D --> E
    D --> F
    D --> G
    E --> H
    F --> I
    G --> I

    J[Push Notifications] --> A
    K[Flutter Workmanager] --> A
```

### Architectural Patterns

- **Offline-First Architecture:** Local SQLite as primary data store with cloud sync - _Rationale:_ Essential for laboratory environments with unreliable connectivity
- **Flutter Bloc Pattern:** Business Logic Components for predictable state management - _Rationale:_ Flutter best practice for scalable, testable applications
- **Repository Pattern:** Abstract data access between local and cloud storage - _Rationale:_ Enables seamless sync and testing isolation
- **Clean Architecture:** Layered dependency structure with domain-driven design - _Rationale:_ Supports complex business logic for culture management
- **Event-Driven Updates:** Stream-based state management for data synchronization - _Rationale:_ Ensures UI consistency during background sync operations using Flutter's reactive paradigm

