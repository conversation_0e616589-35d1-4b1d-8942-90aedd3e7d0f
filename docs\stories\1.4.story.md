# Story 1.4: Create Subculture from Parent Culture

## Status
Draft

## Story
**As a** tissue culture practitioner,
**I want** to create a subculture from an existing culture,
**so that** I can expand my cultures while maintaining lineage tracking.

## Acceptance Criteria
1. Culture detail view includes "Create Subculture" button
2. Subculture form pre-fills Parent Culture ID from source culture
3. Form captures: Date of subculture (defaults to today), Medium composition (text field), Number of explants transferred
4. System assigns unique Subculture ID following parent (e.g., C001 → S001, S002)
5. Subculture appears in timeline as separate entry with parent linkage indicator
6. Parent culture's detail view shows list of all subcultures created from it
7. Subculture record links back to parent culture ID in database
8. Timeline distinguishes between original cultures and subcultures visually

## Tasks / Subtasks
- [ ] Create culture detail view and navigation (AC: 1)
  - [ ] Create `culture_detail_screen.dart` with comprehensive culture information display
  - [ ] Add navigation from timeline culture cards to detail view
  - [ ] Display all culture fields: ID, species, explant type, source plant, initiation date, status, medium, conditions
  - [ ] Include "Create Subculture" button prominently in the detail view
- [ ] Implement subculture database model and schema (AC: 4, 7)
  - [ ] Create `Subculture` entity following data models specification
  - [ ] Add `Subcultures` table to Drift database with proper relationships
  - [ ] Implement SubcultureDao with CRUD operations and parent-child queries
  - [ ] Create indexes for performance: parent_culture_id, subculture_date, status
- [ ] Create subculture creation form and logic (AC: 2, 3, 4)
  - [ ] Create `create_subculture_screen.dart` with pre-filled parent culture information
  - [ ] Implement form validation for required fields (parent ID, date, explant count)
  - [ ] Auto-generate sequential subculture IDs (S001, S002, etc.) based on parent
  - [ ] Save subculture with proper parent linkage in database
- [ ] Update timeline to display subcultures distinctly (AC: 5, 8)
  - [ ] Modify CultureListCubit to load both cultures and subcultures
  - [ ] Create `_SubcultureCard` widget with visual parent linkage indicator
  - [ ] Add visual distinction (icon, color, indentation) between cultures and subcultures
  - [ ] Ensure timeline sorting includes both types chronologically
- [ ] Implement parent-child relationship UI (AC: 6)
  - [ ] Add "Subcultures" section to culture detail view
  - [ ] Display list of all child subcultures with navigation links
  - [ ] Show creation date and current status for each subculture
  - [ ] Handle empty state when no subcultures exist
- [ ] Unit testing for subculture functionality (Testing Standards)
  - [ ] Test Subculture entity serialization and data integrity
  - [ ] Test SubcultureDao CRUD operations and relationship queries
  - [ ] Test subculture ID generation logic
  - [ ] Test timeline integration with mixed culture/subculture display
  - [ ] Test culture detail view rendering and subculture list display

## Dev Notes

### Previous Story Insights
Stories 1.1-1.3 established:
- Working Flutter app with bottom navigation and timeline functionality
- SQLite database with Drift ORM and Culture entity fully implemented
- Timeline screen (`timeline_screen.dart`) displays cultures with BlocBuilder pattern
- Culture creation workflow working with form validation and database storage
- CultureListCubit managing timeline state with real-time Stream updates
- Navigation structure using existing `main_navigation_screen.dart`

### Tech Stack Context
[Source: architecture/tech-stack.md]
- **Flutter Version**: 3.16+ with Dart 3.2+
- **Database**: SQLite + Drift 2.14+ for local data storage and ORM with relationship support
- **State Management**: Flutter Bloc 8.1+ for predictable state management (use Cubit for simple state)
- **Navigation**: Use existing navigation structure, avoid Navigator.push
- **Code Generation**: Build Runner 2.4+ for generating Drift tables and Freezed classes

### Data Models
[Source: architecture/data-models.md]
**Subculture Entity Requirements:**
- **id** (String): UUID primary key for sync across devices
- **subcultureId** (String): Human-readable ID (S001, S002, etc.)
- **parentCultureId** (String): Reference to parent culture ID [Foreign key relationship]
- **subcultureDate** (DateTime): Date of subculturing operation
- **mediumComposition** (String): Medium used for subculture
- **explantCount** (int): Number of explants transferred
- **status** (CultureStatus): Current lifecycle status (same enum as Culture)
- **isDeleted** (bool): Soft delete flag for sync
- **createdAt** (DateTime): Record creation timestamp
- **updatedAt** (DateTime): Last modification timestamp
- **syncVersion** (int): Version for conflict resolution
- **deviceId** (String): Device that last modified record

**Subculture Dart Class:**
```dart
@freezed
class Subculture with _$Subculture {
  const factory Subculture({
    required String id,
    required String subcultureId,
    required String parentCultureId,
    required DateTime subcultureDate,
    required String mediumComposition,
    required int explantCount,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(1) int syncVersion,
    required String deviceId,
  }) = _Subculture;

  factory Subculture.fromJson(Map<String, dynamic> json) => _$SubcultureFromJson(json);
}
```

### Project Structure Guidelines
[Source: architecture/source-tree.md, current project alignment]
**Current Implementation** (Stories 1.1-1.3):
- **Culture Detail Screen**: Create new `lib/screens/culture_detail_screen.dart`
- **Subculture Creation**: Create new `lib/screens/create_subculture_screen.dart`
- **Database**: Extend existing `lib/data/database/database.dart` with Subculture table
- **Subculture Model**: Create new `lib/data/models/subculture.dart`
- **Navigation**: Use existing `lib/screens/main_navigation_screen.dart` patterns

**Database Structure Addition:**
```dart
@DataClassName('SubcultureData')
class Subcultures extends Table {
  TextColumn get id => text()();
  TextColumn get subcultureId => text()();
  TextColumn get parentCultureId => text().references(Cultures, #id)();
  DateTimeColumn get subcultureDate => dateTime()();
  TextColumn get mediumComposition => text()();
  IntColumn get explantCount => integer()();
  IntColumn get status => intEnum<CultureStatus>()();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  IntColumn get syncVersion => integer().withDefault(const Constant(1))();
  TextColumn get deviceId => text()();

  @override
  Set<Column> get primaryKey => {id};
}
```

### UI Implementation Requirements
[Source: architecture/coding-standards.md]
**Widget Patterns (MANDATORY):**
- **Const Constructors**: ALWAYS use const constructors where possible
- **Private Widgets**: ALWAYS create private widget classes (e.g., `_SubcultureCard`, `_SubcultureForm`)
- **Navigation**: Use existing navigation patterns, avoid Navigator.push
- **Arrow Syntax**: ALWAYS use arrow syntax for single expressions (=> expression)
- **Trailing Commas**: ALWAYS add for multi-parameter functions/constructors

**State Management (MANDATORY):**
- **Use Cubit** for simple state (subculture creation, detail view loading)
- **BlocBuilder** for UI state rendering
- **ALWAYS use Freezed** for state classes with .when() pattern
- **Error Handling**: NEVER use SnackBars, ALWAYS use SelectableText.rich for errors

### Subculture ID Generation Logic
**ID Generation Pattern:**
- Parent Culture C001 → First subculture S001, second subculture S002, etc.
- Parent Culture C042 → First subculture S001, second subculture S002, etc.
- Sequential numbering within parent culture scope
- Query existing subcultures for parent to determine next number

```dart
Future<String> generateSubcultureId(String parentCultureId) async {
  final existingSubcultures = await database.subcultureDao
      .getSubculturesByParent(parentCultureId);
  final nextNumber = existingSubcultures.length + 1;
  return 'S${nextNumber.toString().padLeft(3, '0')}';
}
```

### Visual Distinction Requirements
**Timeline Visual Indicators:**
- **Culture Cards**: Existing design with culture icon
- **Subculture Cards**: Add parent linkage indicator (chain icon, indentation, or parent reference)
- **Color Coding**: Same status colors as cultures but with visual marker for subculture type
- **Sorting**: Chronological by creation/subculture date across both types

### Navigation Flow
**User Journey:**
1. Timeline → Tap culture card → Culture detail view
2. Culture detail → "Create Subculture" button → Subculture creation form
3. Subculture form → Save → Return to culture detail with updated subcultures list
4. Timeline shows new subculture with visual distinction

**Navigation Implementation:**
- Use existing navigation patterns from `main_navigation_screen.dart`
- Pass culture ID as parameter to detail screen
- Pass parent culture information to subculture creation form

### Database Integration
[Source: architecture/data-models.md, Stories 1.1-1.3 implementation]
**Query Requirements:**
- **Parent-Child Queries**: Get all subcultures for a specific parent culture
- **Timeline Queries**: Combined query for cultures and subcultures ordered by date
- **Subculture Counting**: Count existing subcultures for ID generation
- **Real-time Updates**: Stream support for timeline updates when subcultures added

**Performance Considerations:**
- **Indexes**: Create indexes on parentCultureId, subcultureDate, status
- **Foreign Key**: Proper relationship between subcultures and parent cultures
- **Efficient Queries**: Use JOIN queries for timeline display with parent information

## Testing
[Source: architecture/cross-platform-testing-strategy.md]
**Test Location**: Create tests in `test/unit/` and `test/widget/` directories
**Testing Framework**: Use Flutter Test (built-in) for unit and widget tests
**Test Coverage Target**: 85% for unit tests covering state management and UI logic
**Database Testing**: Use in-memory database for testing subculture operations and relationships

**Required Test Files:**
- `test/unit/screens/culture_detail_screen_test.dart` - Widget tests for detail view UI
- `test/unit/screens/create_subculture_screen_test.dart` - Widget tests for subculture creation form
- `test/unit/data/models/subculture_test.dart` - Unit tests for Subculture entity
- `test/unit/data/database/subculture_dao_test.dart` - Unit tests for database operations
- `test/unit/bloc/subculture_cubit_test.dart` - Unit tests for subculture state management

**Test Patterns:**
```dart
// Database relationship testing
group('SubcultureDao', () {
  test('should create subculture with proper parent relationship', () async {
    // Arrange: Create parent culture
    final parentCulture = Culture(/* parent data */);
    await database.cultureDao.insertCulture(parentCulture);

    final subculture = Subculture(
      parentCultureId: parentCulture.id,
      /* subculture data */
    );

    // Act
    await database.subcultureDao.insertSubculture(subculture);

    // Assert
    final retrieved = await database.subcultureDao.getSubcultureById(subculture.id);
    expect(retrieved?.parentCultureId, parentCulture.id);
  });
});

// UI integration testing
testWidgets('Culture detail view displays subcultures list', (tester) async {
  await tester.pumpWidget(MaterialApp(
    home: CultureDetailScreen(cultureId: 'test-culture-id'),
  ));
  expect(find.text('Subcultures'), findsOneWidget);
  expect(find.byType(SubcultureCard), findsWidgets);
});
```

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-27 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used
*[To be filled by Dev Agent]*

### Debug Log References
*[To be filled by Dev Agent]*

### Completion Notes List
*[To be filled by Dev Agent]*

### File List
*[To be filled by Dev Agent]*

## QA Results
*[To be filled by QA Agent]*