# Cross-Platform Testing Strategy

### Testing Architecture Overview

CultureStack's testing strategy addresses the unique challenges of cross-platform Flutter development with Google Services integration, offline-first architecture, and multi-device synchronization.

#### Testing Pyramid Structure

```mermaid
graph TB
    A[E2E Tests<br/>Platform-specific integration<br/>5% of test suite] --> B[Integration Tests<br/>Component interaction<br/>25% of test suite]
    B --> C[Unit Tests<br/>Business logic isolation<br/>70% of test suite]

    D[Platform Channel Tests<br/>Google Services mocking] --> B
    E[Database Tests<br/>SQLite operations] --> B
    F[Sync Tests<br/>Offline/online scenarios] --> B
```

### Layer 1: Unit Testing (70% Coverage Target)

#### Business Logic Testing
```dart
// Example: Culture creation business logic testing
@testWidgets('CultureService creates culture with valid data', (tester) async {
  // Arrange
  final mockCultureDao = MockCultureDao();
  final mockEventBus = MockEventBus();
  final cultureService = CultureServiceImpl(mockCultureDao, mockEventBus);

  final request = CreateCultureRequest(
    species: 'Orchid',
    explantType: 'Leaf',
    initiationDate: DateTime.now(),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile conditions',
  );

  when(mockCultureDao.insertCulture(any)).thenAnswer((_) async => 1);

  // Act
  final result = await cultureService.createCulture(request);

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.species, 'Orchid');
  verify(mockCultureDao.insertCulture(any)).called(1);
  verify(mockEventBus.fire(any)).called(1);
});

// Example: Rate limiting logic testing
test('DriveApiRateLimiter respects token bucket limits', () async {
  // Arrange
  final rateLimiter = DriveApiRateLimiter();

  // Act & Assert
  // Should allow initial requests
  expect(await rateLimiter.canProceed(10), true);
  expect(await rateLimiter.canProceed(10), true);

  // Should reject when bucket is empty
  for (int i = 0; i < 100; i++) {
    await rateLimiter.canProceed(10);
  }
  expect(await rateLimiter.canProceed(10), false);
});
```

#### Data Model Testing
```dart
// Freezed data class serialization testing
test('Culture serialization maintains data integrity', () {
  // Arrange
  final originalCulture = Culture(
    id: 'test-id',
    cultureId: 'C001',
    species: 'Orchid',
    explantType: 'Leaf',
    initiationDate: DateTime.parse('2025-01-01'),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile',
    status: CultureStatus.healthy,
  );

  // Act
  final json = originalCulture.toJson();
  final deserializedCulture = Culture.fromJson(json);

  // Assert
  expect(deserializedCulture, equals(originalCulture));
  expect(deserializedCulture.status, CultureStatus.healthy);
});
```

### Layer 2: Integration Testing (25% Coverage Target)

#### Database Integration Testing
```dart
// Database operations with real SQLite
@testWidgets('Culture CRUD operations with database', (tester) async {
  // Arrange
  final database = await $FloorAppDatabase
      .inMemoryDatabaseBuilder()
      .build();
  final cultureDao = database.cultureDao;

  final culture = Culture(
    id: 'test-culture',
    cultureId: 'C001',
    species: 'Test Plant',
    explantType: 'Leaf',
    initiationDate: DateTime.now(),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile',
  );

  // Act & Assert
  await cultureDao.insertCulture(culture.toCompanion());
  final retrieved = await cultureDao.getCultureById('test-culture');
  expect(retrieved?.species, 'Test Plant');

  // Update test
  final updated = culture.copyWith(status: CultureStatus.contaminated);
  await cultureDao.updateCulture(updated);
  final afterUpdate = await cultureDao.getCultureById('test-culture');
  expect(afterUpdate?.status, CultureStatus.contaminated.name);

  // Cleanup
  await database.close();
});
```

#### Platform Channel Integration Testing
```dart
// Google Services platform channel testing with mocking
@testWidgets('Google Sign-In platform channel integration', (tester) async {
  // Arrange
  const MethodChannel channel = MethodChannel('plugins.flutter.io/google_sign_in');
  final List<MethodCall> log = <MethodCall>[];

  // Mock platform channel responses
  tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(
    channel,
    (MethodCall methodCall) async {
      log.add(methodCall);
      switch (methodCall.method) {
        case 'signIn':
          return {
            'displayName': 'Test User',
            'email': '<EMAIL>',
            'id': 'test-user-id',
            'photoUrl': null,
          };
        case 'isSignedIn':
          return true;
        default:
          return null;
      }
    },
  );

  final authService = GoogleAuthService(GoogleSignIn());

  // Act
  final result = await authService.signInWithGoogle();

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.email, '<EMAIL>');
  expect(log, hasLength(1));
  expect(log.first.method, 'signIn');
});
```

#### Sync Integration Testing
```dart
// Offline/online sync scenario testing
@testWidgets('Sync queue processes operations when online', (tester) async {
  // Arrange
  final mockDriveService = MockGoogleDriveService();
  final syncQueueDao = MockSyncQueueDao();
  final syncService = SyncServiceImpl(mockDriveService, syncQueueDao);

  final queuedOperations = [
    SyncOperation(
      entityType: 'Culture',
      entityId: 'culture-1',
      operation: SyncOperationType.create,
      priority: SyncPriority.high,
    ),
    SyncOperation(
      entityType: 'Observation',
      entityId: 'obs-1',
      operation: SyncOperationType.update,
      priority: SyncPriority.normal,
    ),
  ];

  when(syncQueueDao.getPendingOperations()).thenAnswer((_) async => queuedOperations);
  when(mockDriveService.syncWithRateLimit(any))
      .thenAnswer((_) async => Result.success(SyncResult(
            totalOperations: 2,
            successfulOperations: 2,
            failedOperations: 0,
            rateLimitHit: false,
          )));

  // Act
  final result = await syncService.processQueueWhenOnline();

  // Assert
  expect(result.isSuccess, true);
  expect(result.data.successfulOperations, 2);
  verify(mockDriveService.syncWithRateLimit(any)).called(1);
  verify(syncQueueDao.markOperationsComplete(any)).called(1);
});
```

### Layer 3: Platform-Specific End-to-End Testing (5% Coverage Target)

#### Critical User Flow Testing
```dart
// Full user journey: Create culture → Add observation → Sync
@testWidgets('Complete culture management flow', (tester) async {
  app.main();
  await tester.pumpAndSettle();

  // Navigate to create culture
  await tester.tap(find.byKey(Key('add_culture_fab')));
  await tester.pumpAndSettle();

  // Fill culture creation form
  await tester.enterText(find.byKey(Key('species_field')), 'Test Orchid');
  await tester.enterText(find.byKey(Key('explant_type_field')), 'Leaf');
  await tester.enterText(find.byKey(Key('medium_composition_field')), 'MS Medium');

  // Submit form
  await tester.tap(find.byKey(Key('create_culture_button')));
  await tester.pumpAndSettle();

  // Verify culture appears in timeline
  expect(find.text('Test Orchid'), findsOneWidget);
  expect(find.byKey(Key('culture_status_healthy')), findsOneWidget);

  // Navigate to culture detail
  await tester.tap(find.text('Test Orchid'));
  await tester.pumpAndSettle();

  // Add observation
  await tester.tap(find.byKey(Key('add_observation_button')));
  await tester.pumpAndSettle();

  await tester.tap(find.byKey(Key('contamination_toggle')));
  await tester.enterText(find.byKey(Key('observation_notes')), 'Some contamination detected');

  await tester.tap(find.byKey(Key('save_observation_button')));
  await tester.pumpAndSettle();

  // Verify status updated
  expect(find.byKey(Key('culture_status_contaminated')), findsOneWidget);
  expect(find.text('Some contamination detected'), findsOneWidget);
});
```

### Platform Channel Testing Strategy

#### Google Services Mocking Framework
```dart
class GoogleServicesMockFramework {
  static void setupMocks() {
    // Google Sign-In mocking
    const MethodChannel signInChannel = MethodChannel('plugins.flutter.io/google_sign_in');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(signInChannel, _handleSignInCalls);

    // Google Drive API mocking
    const MethodChannel driveChannel = MethodChannel('plugins.flutter.io/google_drive');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(driveChannel, _handleDriveCalls);

    // Play Billing mocking
    const MethodChannel billingChannel = MethodChannel('plugins.flutter.io/in_app_purchase');
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(billingChannel, _handleBillingCalls);
  }

  static Future<dynamic> _handleSignInCalls(MethodCall call) async {
    switch (call.method) {
      case 'signIn':
        return MockGoogleAccount.standardUser().toMap();
      case 'signOut':
        return null;
      case 'isSignedIn':
        return true;
      default:
        throw MissingPluginException();
    }
  }

  static Future<dynamic> _handleDriveCalls(MethodCall call) async {
    switch (call.method) {
      case 'uploadFile':
        // Simulate successful upload
        await Future.delayed(Duration(milliseconds: 100));
        return {'fileId': 'mock-file-${DateTime.now().millisecondsSinceEpoch}'};
      case 'downloadFile':
        return {'content': 'mock-file-content'};
      case 'listFiles':
        return {'files': []};
      default:
        throw MissingPluginException();
    }
  }
}
```

### Testing Environment Configuration

#### Test Database Setup
```dart
// Isolated test database for each test
class TestDatabaseProvider {
  static Future<AppDatabase> createTestDatabase() async {
    return await $FloorAppDatabase
        .inMemoryDatabaseBuilder()
        .addMigrations([/* test migrations */])
        .build();
  }

  static Future<void> seedTestData(AppDatabase database) async {
    // Insert test recipes
    await database.recipeDao.insertRecipe(
      Recipe(
        id: 'test-recipe-1',
        name: 'Test MS Medium',
        ingredients: [
          Ingredient(name: 'MS Salts', concentration: '4.4', unit: 'g/L'),
          Ingredient(name: 'Sucrose', concentration: '30', unit: 'g/L'),
        ],
        difficultyLevel: DifficultyLevel.beginner,
      ).toCompanion(),
    );

    // Insert test cultures
    await database.cultureDao.insertCulture(
      Culture(
        id: 'test-culture-1',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime.now().subtract(Duration(days: 30)),
        mediumComposition: 'Test MS Medium',
        initialConditions: 'Sterile conditions',
      ).toCompanion(),
    );
  }
}
```

### Performance Testing

#### Load Testing for Database Operations
```dart
test('Database handles large number of cultures efficiently', () async {
  final database = await TestDatabaseProvider.createTestDatabase();
  final stopwatch = Stopwatch()..start();

  // Insert 1000 cultures
  for (int i = 0; i < 1000; i++) {
    await database.cultureDao.insertCulture(
      Culture(
        id: 'culture-$i',
        cultureId: 'C${i.toString().padLeft(3, '0')}',
        species: 'Test Species $i',
        explantType: 'Leaf',
        initiationDate: DateTime.now(),
        mediumComposition: 'MS Medium',
        initialConditions: 'Sterile',
      ).toCompanion(),
    );
  }

  stopwatch.stop();
  expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in <5s

  // Test timeline query performance
  stopwatch.reset();
  stopwatch.start();

  final cultures = await database.cultureDao.getAllActiveCultures();

  stopwatch.stop();
  expect(cultures.length, 1000);
  expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should query in <100ms

  await database.close();
});
```

### Continuous Integration Testing Pipeline

#### GitHub Actions Configuration
```yaml
# .github/workflows/test.yml
name: CultureStack Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - run: flutter pub get
      - run: flutter test --coverage
      - uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info

  integration-tests:
    strategy:
      matrix:
        device: [android, ios]
    runs-on: ${{ matrix.device == 'android' && 'ubuntu-latest' || 'macos-latest' }}
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - name: Setup Android SDK (Android only)
        if: matrix.device == 'android'
        uses: android-actions/setup-android@v2
      - name: Setup iOS Simulator (iOS only)
        if: matrix.device == 'ios'
        run: xcrun simctl boot "iPhone 14"
      - run: flutter pub get
      - run: flutter drive --target=test_driver/integration_test.dart
```

### Testing Coverage Goals

| Test Type | Coverage Target | Rationale |
|-----------|----------------|-----------|
| Unit Tests | 85% | Core business logic reliability |
| Integration Tests | 70% | Component interaction validation |
| Platform Channel Tests | 90% | Critical Google Services functionality |
| E2E Tests | 80% of critical paths | User journey reliability |
| Performance Tests | All database operations | Scalability assurance |

### Test Data Management

#### Test Fixtures and Factories
```dart
class CultureTestFactory {
  static Culture createValidCulture({
    String? id,
    String? species,
    CultureStatus? status,
    DateTime? initiationDate,
  }) {
    return Culture(
      id: id ?? 'test-culture-${DateTime.now().millisecondsSinceEpoch}',
      cultureId: 'C001',
      species: species ?? 'Test Orchid',
      explantType: 'Leaf',
      initiationDate: initiationDate ?? DateTime.now(),
      mediumComposition: 'MS Medium',
      initialConditions: 'Sterile conditions',
      status: status ?? CultureStatus.healthy,
    );
  }

  static List<Culture> createCultureBatch(int count) {
    return List.generate(count, (index) => createValidCulture(
      id: 'batch-culture-$index',
      species: 'Batch Species $index',
    ));
  }
}
```

This comprehensive testing strategy ensures reliable cross-platform functionality while addressing the specific challenges of Google Services integration, offline-first architecture, and multi-device synchronization.

