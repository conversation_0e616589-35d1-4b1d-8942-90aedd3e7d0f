import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/data/database/database.dart';
import '../../../lib/data/models/culture_status.dart';
import '../../../lib/screens/create_subculture_screen.dart';

// Mock classes
class MockCultureDatabase extends Mock implements CultureDatabase {}

void main() {
  group('CreateSubcultureScreen', () {
    late MockCultureDatabase mockDatabase;

    setUp(() {
      mockDatabase = MockCultureDatabase();
    });

    testWidgets('displays parent culture information', (tester) async {
      // Arrange
      final parentCulture = Culture(
        id: 'parent-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CreateSubcultureScreen(parentCulture: parentCulture),
        ),
      );

      // Assert
      expect(find.text('Parent Culture'), findsOneWidget);
      expect(find.text('ID: C001'), findsOneWidget);
      expect(find.text('Species: Test Orchid'), findsOneWidget);
      expect(find.text('Explant Type: Leaf'), findsOneWidget);
    });

    testWidgets('displays form fields correctly', (tester) async {
      // Arrange
      final parentCulture = Culture(
        id: 'parent-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CreateSubcultureScreen(parentCulture: parentCulture),
        ),
      );

      // Assert
      expect(find.text('Subculture Date'), findsOneWidget);
      expect(find.text('Medium Composition'), findsOneWidget);
      expect(find.text('Number of Explants'), findsOneWidget);
      expect(find.text('Create Subculture'), findsOneWidget);
    });

    testWidgets('pre-fills medium composition from parent', (tester) async {
      // Arrange
      final parentCulture = Culture(
        id: 'parent-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium with vitamins',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CreateSubcultureScreen(parentCulture: parentCulture),
        ),
      );

      // Assert
      final mediumField = find.widgetWithText(TextFormField, 'MS Medium with vitamins');
      expect(mediumField, findsOneWidget);
    });

    testWidgets('validates required fields', (tester) async {
      // Arrange
      final parentCulture = Culture(
        id: 'parent-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CreateSubcultureScreen(parentCulture: parentCulture),
        ),
      );

      // Clear the medium composition field
      final mediumField = find.widgetWithText(TextFormField, 'MS Medium');
      await tester.tap(mediumField);
      await tester.pumpAndSettle();
      await tester.enterText(mediumField, '');

      // Try to submit without explant count
      final createButton = find.text('Create Subculture');
      await tester.tap(createButton);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Please enter the medium composition'), findsOneWidget);
      expect(find.text('Please enter the number of explants'), findsOneWidget);
    });
  });
}
