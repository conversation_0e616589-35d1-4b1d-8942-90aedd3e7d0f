// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $CulturesTable extends Cultures with TableInfo<$CulturesTable, Culture> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CulturesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _cultureIdMeta =
      const VerificationMeta('cultureId');
  @override
  late final GeneratedColumn<String> cultureId = GeneratedColumn<String>(
      'culture_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _speciesMeta =
      const VerificationMeta('species');
  @override
  late final GeneratedColumn<String> species = GeneratedColumn<String>(
      'species', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _explantTypeMeta =
      const VerificationMeta('explantType');
  @override
  late final GeneratedColumn<String> explantType = GeneratedColumn<String>(
      'explant_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _sourcePlantIdMeta =
      const VerificationMeta('sourcePlantId');
  @override
  late final GeneratedColumn<String> sourcePlantId = GeneratedColumn<String>(
      'source_plant_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _initiationDateMeta =
      const VerificationMeta('initiationDate');
  @override
  late final GeneratedColumn<DateTime> initiationDate =
      GeneratedColumn<DateTime>('initiation_date', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _mediumCompositionMeta =
      const VerificationMeta('mediumComposition');
  @override
  late final GeneratedColumn<String> mediumComposition =
      GeneratedColumn<String>('medium_composition', aliasedName, false,
          type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _recipeIdMeta =
      const VerificationMeta('recipeId');
  @override
  late final GeneratedColumn<String> recipeId = GeneratedColumn<String>(
      'recipe_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _initialConditionsMeta =
      const VerificationMeta('initialConditions');
  @override
  late final GeneratedColumn<String> initialConditions =
      GeneratedColumn<String>('initial_conditions', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<CultureStatus, int> status =
      GeneratedColumn<int>('status', aliasedName, false,
              type: DriftSqlType.int, requiredDuringInsert: true)
          .withConverter<CultureStatus>($CulturesTable.$converterstatus);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _syncedAtMeta =
      const VerificationMeta('syncedAt');
  @override
  late final GeneratedColumn<DateTime> syncedAt = GeneratedColumn<DateTime>(
      'synced_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _isDeletedMeta =
      const VerificationMeta('isDeleted');
  @override
  late final GeneratedColumn<bool> isDeleted = GeneratedColumn<bool>(
      'is_deleted', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_deleted" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        cultureId,
        species,
        explantType,
        sourcePlantId,
        initiationDate,
        mediumComposition,
        recipeId,
        initialConditions,
        status,
        createdAt,
        updatedAt,
        syncedAt,
        isDeleted
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'cultures';
  @override
  VerificationContext validateIntegrity(Insertable<Culture> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('culture_id')) {
      context.handle(_cultureIdMeta,
          cultureId.isAcceptableOrUnknown(data['culture_id']!, _cultureIdMeta));
    } else if (isInserting) {
      context.missing(_cultureIdMeta);
    }
    if (data.containsKey('species')) {
      context.handle(_speciesMeta,
          species.isAcceptableOrUnknown(data['species']!, _speciesMeta));
    } else if (isInserting) {
      context.missing(_speciesMeta);
    }
    if (data.containsKey('explant_type')) {
      context.handle(
          _explantTypeMeta,
          explantType.isAcceptableOrUnknown(
              data['explant_type']!, _explantTypeMeta));
    } else if (isInserting) {
      context.missing(_explantTypeMeta);
    }
    if (data.containsKey('source_plant_id')) {
      context.handle(
          _sourcePlantIdMeta,
          sourcePlantId.isAcceptableOrUnknown(
              data['source_plant_id']!, _sourcePlantIdMeta));
    }
    if (data.containsKey('initiation_date')) {
      context.handle(
          _initiationDateMeta,
          initiationDate.isAcceptableOrUnknown(
              data['initiation_date']!, _initiationDateMeta));
    } else if (isInserting) {
      context.missing(_initiationDateMeta);
    }
    if (data.containsKey('medium_composition')) {
      context.handle(
          _mediumCompositionMeta,
          mediumComposition.isAcceptableOrUnknown(
              data['medium_composition']!, _mediumCompositionMeta));
    } else if (isInserting) {
      context.missing(_mediumCompositionMeta);
    }
    if (data.containsKey('recipe_id')) {
      context.handle(_recipeIdMeta,
          recipeId.isAcceptableOrUnknown(data['recipe_id']!, _recipeIdMeta));
    }
    if (data.containsKey('initial_conditions')) {
      context.handle(
          _initialConditionsMeta,
          initialConditions.isAcceptableOrUnknown(
              data['initial_conditions']!, _initialConditionsMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    if (data.containsKey('synced_at')) {
      context.handle(_syncedAtMeta,
          syncedAt.isAcceptableOrUnknown(data['synced_at']!, _syncedAtMeta));
    }
    if (data.containsKey('is_deleted')) {
      context.handle(_isDeletedMeta,
          isDeleted.isAcceptableOrUnknown(data['is_deleted']!, _isDeletedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Culture map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Culture(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      cultureId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}culture_id'])!,
      species: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}species'])!,
      explantType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}explant_type'])!,
      sourcePlantId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}source_plant_id']),
      initiationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}initiation_date'])!,
      mediumComposition: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}medium_composition'])!,
      recipeId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}recipe_id']),
      initialConditions: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}initial_conditions']),
      status: $CulturesTable.$converterstatus.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}status'])!),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      syncedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}synced_at']),
      isDeleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_deleted'])!,
    );
  }

  @override
  $CulturesTable createAlias(String alias) {
    return $CulturesTable(attachedDatabase, alias);
  }

  static TypeConverter<CultureStatus, int> $converterstatus =
      const CultureStatusConverter();
}

class Culture extends DataClass implements Insertable<Culture> {
  final String id;
  final String cultureId;
  final String species;
  final String explantType;
  final String? sourcePlantId;
  final DateTime initiationDate;
  final String mediumComposition;
  final String? recipeId;
  final String? initialConditions;
  final CultureStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? syncedAt;
  final bool isDeleted;
  const Culture(
      {required this.id,
      required this.cultureId,
      required this.species,
      required this.explantType,
      this.sourcePlantId,
      required this.initiationDate,
      required this.mediumComposition,
      this.recipeId,
      this.initialConditions,
      required this.status,
      required this.createdAt,
      required this.updatedAt,
      this.syncedAt,
      required this.isDeleted});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['culture_id'] = Variable<String>(cultureId);
    map['species'] = Variable<String>(species);
    map['explant_type'] = Variable<String>(explantType);
    if (!nullToAbsent || sourcePlantId != null) {
      map['source_plant_id'] = Variable<String>(sourcePlantId);
    }
    map['initiation_date'] = Variable<DateTime>(initiationDate);
    map['medium_composition'] = Variable<String>(mediumComposition);
    if (!nullToAbsent || recipeId != null) {
      map['recipe_id'] = Variable<String>(recipeId);
    }
    if (!nullToAbsent || initialConditions != null) {
      map['initial_conditions'] = Variable<String>(initialConditions);
    }
    {
      map['status'] =
          Variable<int>($CulturesTable.$converterstatus.toSql(status));
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || syncedAt != null) {
      map['synced_at'] = Variable<DateTime>(syncedAt);
    }
    map['is_deleted'] = Variable<bool>(isDeleted);
    return map;
  }

  CulturesCompanion toCompanion(bool nullToAbsent) {
    return CulturesCompanion(
      id: Value(id),
      cultureId: Value(cultureId),
      species: Value(species),
      explantType: Value(explantType),
      sourcePlantId: sourcePlantId == null && nullToAbsent
          ? const Value.absent()
          : Value(sourcePlantId),
      initiationDate: Value(initiationDate),
      mediumComposition: Value(mediumComposition),
      recipeId: recipeId == null && nullToAbsent
          ? const Value.absent()
          : Value(recipeId),
      initialConditions: initialConditions == null && nullToAbsent
          ? const Value.absent()
          : Value(initialConditions),
      status: Value(status),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      syncedAt: syncedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(syncedAt),
      isDeleted: Value(isDeleted),
    );
  }

  factory Culture.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Culture(
      id: serializer.fromJson<String>(json['id']),
      cultureId: serializer.fromJson<String>(json['cultureId']),
      species: serializer.fromJson<String>(json['species']),
      explantType: serializer.fromJson<String>(json['explantType']),
      sourcePlantId: serializer.fromJson<String?>(json['sourcePlantId']),
      initiationDate: serializer.fromJson<DateTime>(json['initiationDate']),
      mediumComposition: serializer.fromJson<String>(json['mediumComposition']),
      recipeId: serializer.fromJson<String?>(json['recipeId']),
      initialConditions:
          serializer.fromJson<String?>(json['initialConditions']),
      status: serializer.fromJson<CultureStatus>(json['status']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      syncedAt: serializer.fromJson<DateTime?>(json['syncedAt']),
      isDeleted: serializer.fromJson<bool>(json['isDeleted']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'cultureId': serializer.toJson<String>(cultureId),
      'species': serializer.toJson<String>(species),
      'explantType': serializer.toJson<String>(explantType),
      'sourcePlantId': serializer.toJson<String?>(sourcePlantId),
      'initiationDate': serializer.toJson<DateTime>(initiationDate),
      'mediumComposition': serializer.toJson<String>(mediumComposition),
      'recipeId': serializer.toJson<String?>(recipeId),
      'initialConditions': serializer.toJson<String?>(initialConditions),
      'status': serializer.toJson<CultureStatus>(status),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'syncedAt': serializer.toJson<DateTime?>(syncedAt),
      'isDeleted': serializer.toJson<bool>(isDeleted),
    };
  }

  Culture copyWith(
          {String? id,
          String? cultureId,
          String? species,
          String? explantType,
          Value<String?> sourcePlantId = const Value.absent(),
          DateTime? initiationDate,
          String? mediumComposition,
          Value<String?> recipeId = const Value.absent(),
          Value<String?> initialConditions = const Value.absent(),
          CultureStatus? status,
          DateTime? createdAt,
          DateTime? updatedAt,
          Value<DateTime?> syncedAt = const Value.absent(),
          bool? isDeleted}) =>
      Culture(
        id: id ?? this.id,
        cultureId: cultureId ?? this.cultureId,
        species: species ?? this.species,
        explantType: explantType ?? this.explantType,
        sourcePlantId:
            sourcePlantId.present ? sourcePlantId.value : this.sourcePlantId,
        initiationDate: initiationDate ?? this.initiationDate,
        mediumComposition: mediumComposition ?? this.mediumComposition,
        recipeId: recipeId.present ? recipeId.value : this.recipeId,
        initialConditions: initialConditions.present
            ? initialConditions.value
            : this.initialConditions,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        syncedAt: syncedAt.present ? syncedAt.value : this.syncedAt,
        isDeleted: isDeleted ?? this.isDeleted,
      );
  Culture copyWithCompanion(CulturesCompanion data) {
    return Culture(
      id: data.id.present ? data.id.value : this.id,
      cultureId: data.cultureId.present ? data.cultureId.value : this.cultureId,
      species: data.species.present ? data.species.value : this.species,
      explantType:
          data.explantType.present ? data.explantType.value : this.explantType,
      sourcePlantId: data.sourcePlantId.present
          ? data.sourcePlantId.value
          : this.sourcePlantId,
      initiationDate: data.initiationDate.present
          ? data.initiationDate.value
          : this.initiationDate,
      mediumComposition: data.mediumComposition.present
          ? data.mediumComposition.value
          : this.mediumComposition,
      recipeId: data.recipeId.present ? data.recipeId.value : this.recipeId,
      initialConditions: data.initialConditions.present
          ? data.initialConditions.value
          : this.initialConditions,
      status: data.status.present ? data.status.value : this.status,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      syncedAt: data.syncedAt.present ? data.syncedAt.value : this.syncedAt,
      isDeleted: data.isDeleted.present ? data.isDeleted.value : this.isDeleted,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Culture(')
          ..write('id: $id, ')
          ..write('cultureId: $cultureId, ')
          ..write('species: $species, ')
          ..write('explantType: $explantType, ')
          ..write('sourcePlantId: $sourcePlantId, ')
          ..write('initiationDate: $initiationDate, ')
          ..write('mediumComposition: $mediumComposition, ')
          ..write('recipeId: $recipeId, ')
          ..write('initialConditions: $initialConditions, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('syncedAt: $syncedAt, ')
          ..write('isDeleted: $isDeleted')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      cultureId,
      species,
      explantType,
      sourcePlantId,
      initiationDate,
      mediumComposition,
      recipeId,
      initialConditions,
      status,
      createdAt,
      updatedAt,
      syncedAt,
      isDeleted);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Culture &&
          other.id == this.id &&
          other.cultureId == this.cultureId &&
          other.species == this.species &&
          other.explantType == this.explantType &&
          other.sourcePlantId == this.sourcePlantId &&
          other.initiationDate == this.initiationDate &&
          other.mediumComposition == this.mediumComposition &&
          other.recipeId == this.recipeId &&
          other.initialConditions == this.initialConditions &&
          other.status == this.status &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.syncedAt == this.syncedAt &&
          other.isDeleted == this.isDeleted);
}

class CulturesCompanion extends UpdateCompanion<Culture> {
  final Value<String> id;
  final Value<String> cultureId;
  final Value<String> species;
  final Value<String> explantType;
  final Value<String?> sourcePlantId;
  final Value<DateTime> initiationDate;
  final Value<String> mediumComposition;
  final Value<String?> recipeId;
  final Value<String?> initialConditions;
  final Value<CultureStatus> status;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> syncedAt;
  final Value<bool> isDeleted;
  final Value<int> rowid;
  const CulturesCompanion({
    this.id = const Value.absent(),
    this.cultureId = const Value.absent(),
    this.species = const Value.absent(),
    this.explantType = const Value.absent(),
    this.sourcePlantId = const Value.absent(),
    this.initiationDate = const Value.absent(),
    this.mediumComposition = const Value.absent(),
    this.recipeId = const Value.absent(),
    this.initialConditions = const Value.absent(),
    this.status = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.syncedAt = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CulturesCompanion.insert({
    required String id,
    required String cultureId,
    required String species,
    required String explantType,
    this.sourcePlantId = const Value.absent(),
    required DateTime initiationDate,
    required String mediumComposition,
    this.recipeId = const Value.absent(),
    this.initialConditions = const Value.absent(),
    required CultureStatus status,
    required DateTime createdAt,
    required DateTime updatedAt,
    this.syncedAt = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        cultureId = Value(cultureId),
        species = Value(species),
        explantType = Value(explantType),
        initiationDate = Value(initiationDate),
        mediumComposition = Value(mediumComposition),
        status = Value(status),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt);
  static Insertable<Culture> custom({
    Expression<String>? id,
    Expression<String>? cultureId,
    Expression<String>? species,
    Expression<String>? explantType,
    Expression<String>? sourcePlantId,
    Expression<DateTime>? initiationDate,
    Expression<String>? mediumComposition,
    Expression<String>? recipeId,
    Expression<String>? initialConditions,
    Expression<int>? status,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? syncedAt,
    Expression<bool>? isDeleted,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (cultureId != null) 'culture_id': cultureId,
      if (species != null) 'species': species,
      if (explantType != null) 'explant_type': explantType,
      if (sourcePlantId != null) 'source_plant_id': sourcePlantId,
      if (initiationDate != null) 'initiation_date': initiationDate,
      if (mediumComposition != null) 'medium_composition': mediumComposition,
      if (recipeId != null) 'recipe_id': recipeId,
      if (initialConditions != null) 'initial_conditions': initialConditions,
      if (status != null) 'status': status,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (syncedAt != null) 'synced_at': syncedAt,
      if (isDeleted != null) 'is_deleted': isDeleted,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CulturesCompanion copyWith(
      {Value<String>? id,
      Value<String>? cultureId,
      Value<String>? species,
      Value<String>? explantType,
      Value<String?>? sourcePlantId,
      Value<DateTime>? initiationDate,
      Value<String>? mediumComposition,
      Value<String?>? recipeId,
      Value<String?>? initialConditions,
      Value<CultureStatus>? status,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<DateTime?>? syncedAt,
      Value<bool>? isDeleted,
      Value<int>? rowid}) {
    return CulturesCompanion(
      id: id ?? this.id,
      cultureId: cultureId ?? this.cultureId,
      species: species ?? this.species,
      explantType: explantType ?? this.explantType,
      sourcePlantId: sourcePlantId ?? this.sourcePlantId,
      initiationDate: initiationDate ?? this.initiationDate,
      mediumComposition: mediumComposition ?? this.mediumComposition,
      recipeId: recipeId ?? this.recipeId,
      initialConditions: initialConditions ?? this.initialConditions,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (cultureId.present) {
      map['culture_id'] = Variable<String>(cultureId.value);
    }
    if (species.present) {
      map['species'] = Variable<String>(species.value);
    }
    if (explantType.present) {
      map['explant_type'] = Variable<String>(explantType.value);
    }
    if (sourcePlantId.present) {
      map['source_plant_id'] = Variable<String>(sourcePlantId.value);
    }
    if (initiationDate.present) {
      map['initiation_date'] = Variable<DateTime>(initiationDate.value);
    }
    if (mediumComposition.present) {
      map['medium_composition'] = Variable<String>(mediumComposition.value);
    }
    if (recipeId.present) {
      map['recipe_id'] = Variable<String>(recipeId.value);
    }
    if (initialConditions.present) {
      map['initial_conditions'] = Variable<String>(initialConditions.value);
    }
    if (status.present) {
      map['status'] =
          Variable<int>($CulturesTable.$converterstatus.toSql(status.value));
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (syncedAt.present) {
      map['synced_at'] = Variable<DateTime>(syncedAt.value);
    }
    if (isDeleted.present) {
      map['is_deleted'] = Variable<bool>(isDeleted.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CulturesCompanion(')
          ..write('id: $id, ')
          ..write('cultureId: $cultureId, ')
          ..write('species: $species, ')
          ..write('explantType: $explantType, ')
          ..write('sourcePlantId: $sourcePlantId, ')
          ..write('initiationDate: $initiationDate, ')
          ..write('mediumComposition: $mediumComposition, ')
          ..write('recipeId: $recipeId, ')
          ..write('initialConditions: $initialConditions, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('syncedAt: $syncedAt, ')
          ..write('isDeleted: $isDeleted, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SubculturesTable extends Subcultures
    with TableInfo<$SubculturesTable, Subculture> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SubculturesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _subcultureIdMeta =
      const VerificationMeta('subcultureId');
  @override
  late final GeneratedColumn<String> subcultureId = GeneratedColumn<String>(
      'subculture_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _parentCultureIdMeta =
      const VerificationMeta('parentCultureId');
  @override
  late final GeneratedColumn<String> parentCultureId = GeneratedColumn<String>(
      'parent_culture_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('REFERENCES cultures (id)'));
  static const VerificationMeta _subcultureDateMeta =
      const VerificationMeta('subcultureDate');
  @override
  late final GeneratedColumn<DateTime> subcultureDate =
      GeneratedColumn<DateTime>('subculture_date', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _mediumCompositionMeta =
      const VerificationMeta('mediumComposition');
  @override
  late final GeneratedColumn<String> mediumComposition =
      GeneratedColumn<String>('medium_composition', aliasedName, false,
          type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _explantCountMeta =
      const VerificationMeta('explantCount');
  @override
  late final GeneratedColumn<int> explantCount = GeneratedColumn<int>(
      'explant_count', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<CultureStatus, int> status =
      GeneratedColumn<int>('status', aliasedName, false,
              type: DriftSqlType.int, requiredDuringInsert: true)
          .withConverter<CultureStatus>($SubculturesTable.$converterstatus);
  static const VerificationMeta _notesMeta = const VerificationMeta('notes');
  @override
  late final GeneratedColumn<String> notes = GeneratedColumn<String>(
      'notes', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _syncedAtMeta =
      const VerificationMeta('syncedAt');
  @override
  late final GeneratedColumn<DateTime> syncedAt = GeneratedColumn<DateTime>(
      'synced_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _isDeletedMeta =
      const VerificationMeta('isDeleted');
  @override
  late final GeneratedColumn<bool> isDeleted = GeneratedColumn<bool>(
      'is_deleted', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_deleted" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        subcultureId,
        parentCultureId,
        subcultureDate,
        mediumComposition,
        explantCount,
        status,
        notes,
        createdAt,
        updatedAt,
        syncedAt,
        isDeleted
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'subcultures';
  @override
  VerificationContext validateIntegrity(Insertable<Subculture> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('subculture_id')) {
      context.handle(
          _subcultureIdMeta,
          subcultureId.isAcceptableOrUnknown(
              data['subculture_id']!, _subcultureIdMeta));
    } else if (isInserting) {
      context.missing(_subcultureIdMeta);
    }
    if (data.containsKey('parent_culture_id')) {
      context.handle(
          _parentCultureIdMeta,
          parentCultureId.isAcceptableOrUnknown(
              data['parent_culture_id']!, _parentCultureIdMeta));
    } else if (isInserting) {
      context.missing(_parentCultureIdMeta);
    }
    if (data.containsKey('subculture_date')) {
      context.handle(
          _subcultureDateMeta,
          subcultureDate.isAcceptableOrUnknown(
              data['subculture_date']!, _subcultureDateMeta));
    } else if (isInserting) {
      context.missing(_subcultureDateMeta);
    }
    if (data.containsKey('medium_composition')) {
      context.handle(
          _mediumCompositionMeta,
          mediumComposition.isAcceptableOrUnknown(
              data['medium_composition']!, _mediumCompositionMeta));
    } else if (isInserting) {
      context.missing(_mediumCompositionMeta);
    }
    if (data.containsKey('explant_count')) {
      context.handle(
          _explantCountMeta,
          explantCount.isAcceptableOrUnknown(
              data['explant_count']!, _explantCountMeta));
    } else if (isInserting) {
      context.missing(_explantCountMeta);
    }
    if (data.containsKey('notes')) {
      context.handle(
          _notesMeta, notes.isAcceptableOrUnknown(data['notes']!, _notesMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    if (data.containsKey('synced_at')) {
      context.handle(_syncedAtMeta,
          syncedAt.isAcceptableOrUnknown(data['synced_at']!, _syncedAtMeta));
    }
    if (data.containsKey('is_deleted')) {
      context.handle(_isDeletedMeta,
          isDeleted.isAcceptableOrUnknown(data['is_deleted']!, _isDeletedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Subculture map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Subculture(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      subcultureId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}subculture_id'])!,
      parentCultureId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}parent_culture_id'])!,
      subcultureDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}subculture_date'])!,
      mediumComposition: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}medium_composition'])!,
      explantCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}explant_count'])!,
      status: $SubculturesTable.$converterstatus.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}status'])!),
      notes: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}notes']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      syncedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}synced_at']),
      isDeleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_deleted'])!,
    );
  }

  @override
  $SubculturesTable createAlias(String alias) {
    return $SubculturesTable(attachedDatabase, alias);
  }

  static TypeConverter<CultureStatus, int> $converterstatus =
      const CultureStatusConverter();
}

class Subculture extends DataClass implements Insertable<Subculture> {
  final String id;
  final String subcultureId;
  final String parentCultureId;
  final DateTime subcultureDate;
  final String mediumComposition;
  final int explantCount;
  final CultureStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? syncedAt;
  final bool isDeleted;
  const Subculture(
      {required this.id,
      required this.subcultureId,
      required this.parentCultureId,
      required this.subcultureDate,
      required this.mediumComposition,
      required this.explantCount,
      required this.status,
      this.notes,
      required this.createdAt,
      required this.updatedAt,
      this.syncedAt,
      required this.isDeleted});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['subculture_id'] = Variable<String>(subcultureId);
    map['parent_culture_id'] = Variable<String>(parentCultureId);
    map['subculture_date'] = Variable<DateTime>(subcultureDate);
    map['medium_composition'] = Variable<String>(mediumComposition);
    map['explant_count'] = Variable<int>(explantCount);
    {
      map['status'] =
          Variable<int>($SubculturesTable.$converterstatus.toSql(status));
    }
    if (!nullToAbsent || notes != null) {
      map['notes'] = Variable<String>(notes);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || syncedAt != null) {
      map['synced_at'] = Variable<DateTime>(syncedAt);
    }
    map['is_deleted'] = Variable<bool>(isDeleted);
    return map;
  }

  SubculturesCompanion toCompanion(bool nullToAbsent) {
    return SubculturesCompanion(
      id: Value(id),
      subcultureId: Value(subcultureId),
      parentCultureId: Value(parentCultureId),
      subcultureDate: Value(subcultureDate),
      mediumComposition: Value(mediumComposition),
      explantCount: Value(explantCount),
      status: Value(status),
      notes:
          notes == null && nullToAbsent ? const Value.absent() : Value(notes),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      syncedAt: syncedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(syncedAt),
      isDeleted: Value(isDeleted),
    );
  }

  factory Subculture.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Subculture(
      id: serializer.fromJson<String>(json['id']),
      subcultureId: serializer.fromJson<String>(json['subcultureId']),
      parentCultureId: serializer.fromJson<String>(json['parentCultureId']),
      subcultureDate: serializer.fromJson<DateTime>(json['subcultureDate']),
      mediumComposition: serializer.fromJson<String>(json['mediumComposition']),
      explantCount: serializer.fromJson<int>(json['explantCount']),
      status: serializer.fromJson<CultureStatus>(json['status']),
      notes: serializer.fromJson<String?>(json['notes']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      syncedAt: serializer.fromJson<DateTime?>(json['syncedAt']),
      isDeleted: serializer.fromJson<bool>(json['isDeleted']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'subcultureId': serializer.toJson<String>(subcultureId),
      'parentCultureId': serializer.toJson<String>(parentCultureId),
      'subcultureDate': serializer.toJson<DateTime>(subcultureDate),
      'mediumComposition': serializer.toJson<String>(mediumComposition),
      'explantCount': serializer.toJson<int>(explantCount),
      'status': serializer.toJson<CultureStatus>(status),
      'notes': serializer.toJson<String?>(notes),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'syncedAt': serializer.toJson<DateTime?>(syncedAt),
      'isDeleted': serializer.toJson<bool>(isDeleted),
    };
  }

  Subculture copyWith(
          {String? id,
          String? subcultureId,
          String? parentCultureId,
          DateTime? subcultureDate,
          String? mediumComposition,
          int? explantCount,
          CultureStatus? status,
          Value<String?> notes = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt,
          Value<DateTime?> syncedAt = const Value.absent(),
          bool? isDeleted}) =>
      Subculture(
        id: id ?? this.id,
        subcultureId: subcultureId ?? this.subcultureId,
        parentCultureId: parentCultureId ?? this.parentCultureId,
        subcultureDate: subcultureDate ?? this.subcultureDate,
        mediumComposition: mediumComposition ?? this.mediumComposition,
        explantCount: explantCount ?? this.explantCount,
        status: status ?? this.status,
        notes: notes.present ? notes.value : this.notes,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        syncedAt: syncedAt.present ? syncedAt.value : this.syncedAt,
        isDeleted: isDeleted ?? this.isDeleted,
      );
  Subculture copyWithCompanion(SubculturesCompanion data) {
    return Subculture(
      id: data.id.present ? data.id.value : this.id,
      subcultureId: data.subcultureId.present
          ? data.subcultureId.value
          : this.subcultureId,
      parentCultureId: data.parentCultureId.present
          ? data.parentCultureId.value
          : this.parentCultureId,
      subcultureDate: data.subcultureDate.present
          ? data.subcultureDate.value
          : this.subcultureDate,
      mediumComposition: data.mediumComposition.present
          ? data.mediumComposition.value
          : this.mediumComposition,
      explantCount: data.explantCount.present
          ? data.explantCount.value
          : this.explantCount,
      status: data.status.present ? data.status.value : this.status,
      notes: data.notes.present ? data.notes.value : this.notes,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      syncedAt: data.syncedAt.present ? data.syncedAt.value : this.syncedAt,
      isDeleted: data.isDeleted.present ? data.isDeleted.value : this.isDeleted,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Subculture(')
          ..write('id: $id, ')
          ..write('subcultureId: $subcultureId, ')
          ..write('parentCultureId: $parentCultureId, ')
          ..write('subcultureDate: $subcultureDate, ')
          ..write('mediumComposition: $mediumComposition, ')
          ..write('explantCount: $explantCount, ')
          ..write('status: $status, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('syncedAt: $syncedAt, ')
          ..write('isDeleted: $isDeleted')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      subcultureId,
      parentCultureId,
      subcultureDate,
      mediumComposition,
      explantCount,
      status,
      notes,
      createdAt,
      updatedAt,
      syncedAt,
      isDeleted);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Subculture &&
          other.id == this.id &&
          other.subcultureId == this.subcultureId &&
          other.parentCultureId == this.parentCultureId &&
          other.subcultureDate == this.subcultureDate &&
          other.mediumComposition == this.mediumComposition &&
          other.explantCount == this.explantCount &&
          other.status == this.status &&
          other.notes == this.notes &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.syncedAt == this.syncedAt &&
          other.isDeleted == this.isDeleted);
}

class SubculturesCompanion extends UpdateCompanion<Subculture> {
  final Value<String> id;
  final Value<String> subcultureId;
  final Value<String> parentCultureId;
  final Value<DateTime> subcultureDate;
  final Value<String> mediumComposition;
  final Value<int> explantCount;
  final Value<CultureStatus> status;
  final Value<String?> notes;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> syncedAt;
  final Value<bool> isDeleted;
  final Value<int> rowid;
  const SubculturesCompanion({
    this.id = const Value.absent(),
    this.subcultureId = const Value.absent(),
    this.parentCultureId = const Value.absent(),
    this.subcultureDate = const Value.absent(),
    this.mediumComposition = const Value.absent(),
    this.explantCount = const Value.absent(),
    this.status = const Value.absent(),
    this.notes = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.syncedAt = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  SubculturesCompanion.insert({
    required String id,
    required String subcultureId,
    required String parentCultureId,
    required DateTime subcultureDate,
    required String mediumComposition,
    required int explantCount,
    required CultureStatus status,
    this.notes = const Value.absent(),
    required DateTime createdAt,
    required DateTime updatedAt,
    this.syncedAt = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        subcultureId = Value(subcultureId),
        parentCultureId = Value(parentCultureId),
        subcultureDate = Value(subcultureDate),
        mediumComposition = Value(mediumComposition),
        explantCount = Value(explantCount),
        status = Value(status),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt);
  static Insertable<Subculture> custom({
    Expression<String>? id,
    Expression<String>? subcultureId,
    Expression<String>? parentCultureId,
    Expression<DateTime>? subcultureDate,
    Expression<String>? mediumComposition,
    Expression<int>? explantCount,
    Expression<int>? status,
    Expression<String>? notes,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? syncedAt,
    Expression<bool>? isDeleted,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (subcultureId != null) 'subculture_id': subcultureId,
      if (parentCultureId != null) 'parent_culture_id': parentCultureId,
      if (subcultureDate != null) 'subculture_date': subcultureDate,
      if (mediumComposition != null) 'medium_composition': mediumComposition,
      if (explantCount != null) 'explant_count': explantCount,
      if (status != null) 'status': status,
      if (notes != null) 'notes': notes,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (syncedAt != null) 'synced_at': syncedAt,
      if (isDeleted != null) 'is_deleted': isDeleted,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SubculturesCompanion copyWith(
      {Value<String>? id,
      Value<String>? subcultureId,
      Value<String>? parentCultureId,
      Value<DateTime>? subcultureDate,
      Value<String>? mediumComposition,
      Value<int>? explantCount,
      Value<CultureStatus>? status,
      Value<String?>? notes,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<DateTime?>? syncedAt,
      Value<bool>? isDeleted,
      Value<int>? rowid}) {
    return SubculturesCompanion(
      id: id ?? this.id,
      subcultureId: subcultureId ?? this.subcultureId,
      parentCultureId: parentCultureId ?? this.parentCultureId,
      subcultureDate: subcultureDate ?? this.subcultureDate,
      mediumComposition: mediumComposition ?? this.mediumComposition,
      explantCount: explantCount ?? this.explantCount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (subcultureId.present) {
      map['subculture_id'] = Variable<String>(subcultureId.value);
    }
    if (parentCultureId.present) {
      map['parent_culture_id'] = Variable<String>(parentCultureId.value);
    }
    if (subcultureDate.present) {
      map['subculture_date'] = Variable<DateTime>(subcultureDate.value);
    }
    if (mediumComposition.present) {
      map['medium_composition'] = Variable<String>(mediumComposition.value);
    }
    if (explantCount.present) {
      map['explant_count'] = Variable<int>(explantCount.value);
    }
    if (status.present) {
      map['status'] =
          Variable<int>($SubculturesTable.$converterstatus.toSql(status.value));
    }
    if (notes.present) {
      map['notes'] = Variable<String>(notes.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (syncedAt.present) {
      map['synced_at'] = Variable<DateTime>(syncedAt.value);
    }
    if (isDeleted.present) {
      map['is_deleted'] = Variable<bool>(isDeleted.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SubculturesCompanion(')
          ..write('id: $id, ')
          ..write('subcultureId: $subcultureId, ')
          ..write('parentCultureId: $parentCultureId, ')
          ..write('subcultureDate: $subcultureDate, ')
          ..write('mediumComposition: $mediumComposition, ')
          ..write('explantCount: $explantCount, ')
          ..write('status: $status, ')
          ..write('notes: $notes, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('syncedAt: $syncedAt, ')
          ..write('isDeleted: $isDeleted, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$CultureDatabase extends GeneratedDatabase {
  _$CultureDatabase(QueryExecutor e) : super(e);
  $CultureDatabaseManager get managers => $CultureDatabaseManager(this);
  late final $CulturesTable cultures = $CulturesTable(this);
  late final $SubculturesTable subcultures = $SubculturesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [cultures, subcultures];
}

typedef $$CulturesTableCreateCompanionBuilder = CulturesCompanion Function({
  required String id,
  required String cultureId,
  required String species,
  required String explantType,
  Value<String?> sourcePlantId,
  required DateTime initiationDate,
  required String mediumComposition,
  Value<String?> recipeId,
  Value<String?> initialConditions,
  required CultureStatus status,
  required DateTime createdAt,
  required DateTime updatedAt,
  Value<DateTime?> syncedAt,
  Value<bool> isDeleted,
  Value<int> rowid,
});
typedef $$CulturesTableUpdateCompanionBuilder = CulturesCompanion Function({
  Value<String> id,
  Value<String> cultureId,
  Value<String> species,
  Value<String> explantType,
  Value<String?> sourcePlantId,
  Value<DateTime> initiationDate,
  Value<String> mediumComposition,
  Value<String?> recipeId,
  Value<String?> initialConditions,
  Value<CultureStatus> status,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<DateTime?> syncedAt,
  Value<bool> isDeleted,
  Value<int> rowid,
});

final class $$CulturesTableReferences
    extends BaseReferences<_$CultureDatabase, $CulturesTable, Culture> {
  $$CulturesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$SubculturesTable, List<Subculture>>
      _subculturesRefsTable(_$CultureDatabase db) =>
          MultiTypedResultKey.fromTable(db.subcultures,
              aliasName: $_aliasNameGenerator(
                  db.cultures.id, db.subcultures.parentCultureId));

  $$SubculturesTableProcessedTableManager get subculturesRefs {
    final manager = $$SubculturesTableTableManager($_db, $_db.subcultures)
        .filter(
            (f) => f.parentCultureId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_subculturesRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$CulturesTableFilterComposer
    extends Composer<_$CultureDatabase, $CulturesTable> {
  $$CulturesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get cultureId => $composableBuilder(
      column: $table.cultureId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get species => $composableBuilder(
      column: $table.species, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get explantType => $composableBuilder(
      column: $table.explantType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get sourcePlantId => $composableBuilder(
      column: $table.sourcePlantId, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get initiationDate => $composableBuilder(
      column: $table.initiationDate,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get recipeId => $composableBuilder(
      column: $table.recipeId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get initialConditions => $composableBuilder(
      column: $table.initialConditions,
      builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<CultureStatus, CultureStatus, int>
      get status => $composableBuilder(
          column: $table.status,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get syncedAt => $composableBuilder(
      column: $table.syncedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isDeleted => $composableBuilder(
      column: $table.isDeleted, builder: (column) => ColumnFilters(column));

  Expression<bool> subculturesRefs(
      Expression<bool> Function($$SubculturesTableFilterComposer f) f) {
    final $$SubculturesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.subcultures,
        getReferencedColumn: (t) => t.parentCultureId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SubculturesTableFilterComposer(
              $db: $db,
              $table: $db.subcultures,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$CulturesTableOrderingComposer
    extends Composer<_$CultureDatabase, $CulturesTable> {
  $$CulturesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get cultureId => $composableBuilder(
      column: $table.cultureId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get species => $composableBuilder(
      column: $table.species, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get explantType => $composableBuilder(
      column: $table.explantType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get sourcePlantId => $composableBuilder(
      column: $table.sourcePlantId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get initiationDate => $composableBuilder(
      column: $table.initiationDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get recipeId => $composableBuilder(
      column: $table.recipeId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get initialConditions => $composableBuilder(
      column: $table.initialConditions,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get syncedAt => $composableBuilder(
      column: $table.syncedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isDeleted => $composableBuilder(
      column: $table.isDeleted, builder: (column) => ColumnOrderings(column));
}

class $$CulturesTableAnnotationComposer
    extends Composer<_$CultureDatabase, $CulturesTable> {
  $$CulturesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get cultureId =>
      $composableBuilder(column: $table.cultureId, builder: (column) => column);

  GeneratedColumn<String> get species =>
      $composableBuilder(column: $table.species, builder: (column) => column);

  GeneratedColumn<String> get explantType => $composableBuilder(
      column: $table.explantType, builder: (column) => column);

  GeneratedColumn<String> get sourcePlantId => $composableBuilder(
      column: $table.sourcePlantId, builder: (column) => column);

  GeneratedColumn<DateTime> get initiationDate => $composableBuilder(
      column: $table.initiationDate, builder: (column) => column);

  GeneratedColumn<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition, builder: (column) => column);

  GeneratedColumn<String> get recipeId =>
      $composableBuilder(column: $table.recipeId, builder: (column) => column);

  GeneratedColumn<String> get initialConditions => $composableBuilder(
      column: $table.initialConditions, builder: (column) => column);

  GeneratedColumnWithTypeConverter<CultureStatus, int> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get syncedAt =>
      $composableBuilder(column: $table.syncedAt, builder: (column) => column);

  GeneratedColumn<bool> get isDeleted =>
      $composableBuilder(column: $table.isDeleted, builder: (column) => column);

  Expression<T> subculturesRefs<T extends Object>(
      Expression<T> Function($$SubculturesTableAnnotationComposer a) f) {
    final $$SubculturesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.subcultures,
        getReferencedColumn: (t) => t.parentCultureId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SubculturesTableAnnotationComposer(
              $db: $db,
              $table: $db.subcultures,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$CulturesTableTableManager extends RootTableManager<
    _$CultureDatabase,
    $CulturesTable,
    Culture,
    $$CulturesTableFilterComposer,
    $$CulturesTableOrderingComposer,
    $$CulturesTableAnnotationComposer,
    $$CulturesTableCreateCompanionBuilder,
    $$CulturesTableUpdateCompanionBuilder,
    (Culture, $$CulturesTableReferences),
    Culture,
    PrefetchHooks Function({bool subculturesRefs})> {
  $$CulturesTableTableManager(_$CultureDatabase db, $CulturesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CulturesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CulturesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CulturesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> cultureId = const Value.absent(),
            Value<String> species = const Value.absent(),
            Value<String> explantType = const Value.absent(),
            Value<String?> sourcePlantId = const Value.absent(),
            Value<DateTime> initiationDate = const Value.absent(),
            Value<String> mediumComposition = const Value.absent(),
            Value<String?> recipeId = const Value.absent(),
            Value<String?> initialConditions = const Value.absent(),
            Value<CultureStatus> status = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<DateTime?> syncedAt = const Value.absent(),
            Value<bool> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CulturesCompanion(
            id: id,
            cultureId: cultureId,
            species: species,
            explantType: explantType,
            sourcePlantId: sourcePlantId,
            initiationDate: initiationDate,
            mediumComposition: mediumComposition,
            recipeId: recipeId,
            initialConditions: initialConditions,
            status: status,
            createdAt: createdAt,
            updatedAt: updatedAt,
            syncedAt: syncedAt,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String cultureId,
            required String species,
            required String explantType,
            Value<String?> sourcePlantId = const Value.absent(),
            required DateTime initiationDate,
            required String mediumComposition,
            Value<String?> recipeId = const Value.absent(),
            Value<String?> initialConditions = const Value.absent(),
            required CultureStatus status,
            required DateTime createdAt,
            required DateTime updatedAt,
            Value<DateTime?> syncedAt = const Value.absent(),
            Value<bool> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CulturesCompanion.insert(
            id: id,
            cultureId: cultureId,
            species: species,
            explantType: explantType,
            sourcePlantId: sourcePlantId,
            initiationDate: initiationDate,
            mediumComposition: mediumComposition,
            recipeId: recipeId,
            initialConditions: initialConditions,
            status: status,
            createdAt: createdAt,
            updatedAt: updatedAt,
            syncedAt: syncedAt,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$CulturesTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({subculturesRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (subculturesRefs) db.subcultures],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (subculturesRefs)
                    await $_getPrefetchedData<Culture, $CulturesTable,
                            Subculture>(
                        currentTable: table,
                        referencedTable:
                            $$CulturesTableReferences._subculturesRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CulturesTableReferences(db, table, p0)
                                .subculturesRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.parentCultureId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$CulturesTableProcessedTableManager = ProcessedTableManager<
    _$CultureDatabase,
    $CulturesTable,
    Culture,
    $$CulturesTableFilterComposer,
    $$CulturesTableOrderingComposer,
    $$CulturesTableAnnotationComposer,
    $$CulturesTableCreateCompanionBuilder,
    $$CulturesTableUpdateCompanionBuilder,
    (Culture, $$CulturesTableReferences),
    Culture,
    PrefetchHooks Function({bool subculturesRefs})>;
typedef $$SubculturesTableCreateCompanionBuilder = SubculturesCompanion
    Function({
  required String id,
  required String subcultureId,
  required String parentCultureId,
  required DateTime subcultureDate,
  required String mediumComposition,
  required int explantCount,
  required CultureStatus status,
  Value<String?> notes,
  required DateTime createdAt,
  required DateTime updatedAt,
  Value<DateTime?> syncedAt,
  Value<bool> isDeleted,
  Value<int> rowid,
});
typedef $$SubculturesTableUpdateCompanionBuilder = SubculturesCompanion
    Function({
  Value<String> id,
  Value<String> subcultureId,
  Value<String> parentCultureId,
  Value<DateTime> subcultureDate,
  Value<String> mediumComposition,
  Value<int> explantCount,
  Value<CultureStatus> status,
  Value<String?> notes,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<DateTime?> syncedAt,
  Value<bool> isDeleted,
  Value<int> rowid,
});

final class $$SubculturesTableReferences
    extends BaseReferences<_$CultureDatabase, $SubculturesTable, Subculture> {
  $$SubculturesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $CulturesTable _parentCultureIdTable(_$CultureDatabase db) =>
      db.cultures.createAlias(
          $_aliasNameGenerator(db.subcultures.parentCultureId, db.cultures.id));

  $$CulturesTableProcessedTableManager get parentCultureId {
    final $_column = $_itemColumn<String>('parent_culture_id')!;

    final manager = $$CulturesTableTableManager($_db, $_db.cultures)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_parentCultureIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$SubculturesTableFilterComposer
    extends Composer<_$CultureDatabase, $SubculturesTable> {
  $$SubculturesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get subcultureId => $composableBuilder(
      column: $table.subcultureId, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get subcultureDate => $composableBuilder(
      column: $table.subcultureDate,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get explantCount => $composableBuilder(
      column: $table.explantCount, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<CultureStatus, CultureStatus, int>
      get status => $composableBuilder(
          column: $table.status,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get notes => $composableBuilder(
      column: $table.notes, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get syncedAt => $composableBuilder(
      column: $table.syncedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isDeleted => $composableBuilder(
      column: $table.isDeleted, builder: (column) => ColumnFilters(column));

  $$CulturesTableFilterComposer get parentCultureId {
    final $$CulturesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentCultureId,
        referencedTable: $db.cultures,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CulturesTableFilterComposer(
              $db: $db,
              $table: $db.cultures,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubculturesTableOrderingComposer
    extends Composer<_$CultureDatabase, $SubculturesTable> {
  $$SubculturesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get subcultureId => $composableBuilder(
      column: $table.subcultureId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get subcultureDate => $composableBuilder(
      column: $table.subcultureDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get explantCount => $composableBuilder(
      column: $table.explantCount,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get notes => $composableBuilder(
      column: $table.notes, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get syncedAt => $composableBuilder(
      column: $table.syncedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isDeleted => $composableBuilder(
      column: $table.isDeleted, builder: (column) => ColumnOrderings(column));

  $$CulturesTableOrderingComposer get parentCultureId {
    final $$CulturesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentCultureId,
        referencedTable: $db.cultures,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CulturesTableOrderingComposer(
              $db: $db,
              $table: $db.cultures,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubculturesTableAnnotationComposer
    extends Composer<_$CultureDatabase, $SubculturesTable> {
  $$SubculturesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get subcultureId => $composableBuilder(
      column: $table.subcultureId, builder: (column) => column);

  GeneratedColumn<DateTime> get subcultureDate => $composableBuilder(
      column: $table.subcultureDate, builder: (column) => column);

  GeneratedColumn<String> get mediumComposition => $composableBuilder(
      column: $table.mediumComposition, builder: (column) => column);

  GeneratedColumn<int> get explantCount => $composableBuilder(
      column: $table.explantCount, builder: (column) => column);

  GeneratedColumnWithTypeConverter<CultureStatus, int> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<String> get notes =>
      $composableBuilder(column: $table.notes, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get syncedAt =>
      $composableBuilder(column: $table.syncedAt, builder: (column) => column);

  GeneratedColumn<bool> get isDeleted =>
      $composableBuilder(column: $table.isDeleted, builder: (column) => column);

  $$CulturesTableAnnotationComposer get parentCultureId {
    final $$CulturesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentCultureId,
        referencedTable: $db.cultures,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$CulturesTableAnnotationComposer(
              $db: $db,
              $table: $db.cultures,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubculturesTableTableManager extends RootTableManager<
    _$CultureDatabase,
    $SubculturesTable,
    Subculture,
    $$SubculturesTableFilterComposer,
    $$SubculturesTableOrderingComposer,
    $$SubculturesTableAnnotationComposer,
    $$SubculturesTableCreateCompanionBuilder,
    $$SubculturesTableUpdateCompanionBuilder,
    (Subculture, $$SubculturesTableReferences),
    Subculture,
    PrefetchHooks Function({bool parentCultureId})> {
  $$SubculturesTableTableManager(_$CultureDatabase db, $SubculturesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SubculturesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SubculturesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SubculturesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> subcultureId = const Value.absent(),
            Value<String> parentCultureId = const Value.absent(),
            Value<DateTime> subcultureDate = const Value.absent(),
            Value<String> mediumComposition = const Value.absent(),
            Value<int> explantCount = const Value.absent(),
            Value<CultureStatus> status = const Value.absent(),
            Value<String?> notes = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<DateTime?> syncedAt = const Value.absent(),
            Value<bool> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SubculturesCompanion(
            id: id,
            subcultureId: subcultureId,
            parentCultureId: parentCultureId,
            subcultureDate: subcultureDate,
            mediumComposition: mediumComposition,
            explantCount: explantCount,
            status: status,
            notes: notes,
            createdAt: createdAt,
            updatedAt: updatedAt,
            syncedAt: syncedAt,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String subcultureId,
            required String parentCultureId,
            required DateTime subcultureDate,
            required String mediumComposition,
            required int explantCount,
            required CultureStatus status,
            Value<String?> notes = const Value.absent(),
            required DateTime createdAt,
            required DateTime updatedAt,
            Value<DateTime?> syncedAt = const Value.absent(),
            Value<bool> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SubculturesCompanion.insert(
            id: id,
            subcultureId: subcultureId,
            parentCultureId: parentCultureId,
            subcultureDate: subcultureDate,
            mediumComposition: mediumComposition,
            explantCount: explantCount,
            status: status,
            notes: notes,
            createdAt: createdAt,
            updatedAt: updatedAt,
            syncedAt: syncedAt,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$SubculturesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({parentCultureId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (parentCultureId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.parentCultureId,
                    referencedTable:
                        $$SubculturesTableReferences._parentCultureIdTable(db),
                    referencedColumn: $$SubculturesTableReferences
                        ._parentCultureIdTable(db)
                        .id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$SubculturesTableProcessedTableManager = ProcessedTableManager<
    _$CultureDatabase,
    $SubculturesTable,
    Subculture,
    $$SubculturesTableFilterComposer,
    $$SubculturesTableOrderingComposer,
    $$SubculturesTableAnnotationComposer,
    $$SubculturesTableCreateCompanionBuilder,
    $$SubculturesTableUpdateCompanionBuilder,
    (Subculture, $$SubculturesTableReferences),
    Subculture,
    PrefetchHooks Function({bool parentCultureId})>;

class $CultureDatabaseManager {
  final _$CultureDatabase _db;
  $CultureDatabaseManager(this._db);
  $$CulturesTableTableManager get cultures =>
      $$CulturesTableTableManager(_db, _db.cultures);
  $$SubculturesTableTableManager get subcultures =>
      $$SubculturesTableTableManager(_db, _db.subcultures);
}
