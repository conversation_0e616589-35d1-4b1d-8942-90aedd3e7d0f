import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/data/database/database.dart';
import '../../../lib/data/models/culture_status.dart';
import '../../../lib/screens/culture_detail_screen.dart';

// Mock classes
class MockCultureDatabase extends Mock implements CultureDatabase {}

void main() {
  group('CultureDetailScreen', () {
    late MockCultureDatabase mockDatabase;

    setUp(() {
      mockDatabase = MockCultureDatabase();
    });

    testWidgets('displays culture information correctly', (tester) async {
      // Arrange
      final testCulture = Culture(
        id: 'test-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      when(mockDatabase.getCultureById('test-culture-id'))
          .thenAnswer((_) async => testCulture);
      when(mockDatabase.getSubculturesForCulture('test-culture-id'))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CultureDetailScreen(cultureId: 'test-culture-id'),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('C001'), findsOneWidget);
      expect(find.text('Test Orchid'), findsOneWidget);
      expect(find.text('Leaf'), findsOneWidget);
      expect(find.text('Create Subculture'), findsOneWidget);
    });

    testWidgets('displays subcultures section', (tester) async {
      // Arrange
      final testCulture = Culture(
        id: 'test-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      when(mockDatabase.getCultureById('test-culture-id'))
          .thenAnswer((_) async => testCulture);
      when(mockDatabase.getSubculturesForCulture('test-culture-id'))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CultureDetailScreen(cultureId: 'test-culture-id'),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.textContaining('Subcultures'), findsOneWidget);
      expect(find.text('No Subcultures Yet'), findsOneWidget);
    });

    testWidgets('displays subcultures when they exist', (tester) async {
      // Arrange
      final testCulture = Culture(
        id: 'test-culture-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
      );

      final testSubculture = Subculture(
        id: 'test-subculture-id',
        subcultureId: 'S001',
        parentCultureId: 'test-culture-id',
        subcultureDate: DateTime(2025, 1, 15),
        mediumComposition: 'MS Medium',
        explantCount: 5,
        status: CultureStatus.healthy,
        createdAt: DateTime(2025, 1, 15),
        updatedAt: DateTime(2025, 1, 15),
      );

      when(mockDatabase.getCultureById('test-culture-id'))
          .thenAnswer((_) async => testCulture);
      when(mockDatabase.getSubculturesForCulture('test-culture-id'))
          .thenAnswer((_) async => [testSubculture]);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CultureDetailScreen(cultureId: 'test-culture-id'),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('S001'), findsOneWidget);
      expect(find.text('5 explants'), findsOneWidget);
    });
  });
}
