# Technical Assumptions

### Repository Structure: Monorepo
Single repository containing the complete Flutter application with organized package structure for maintainability.

### Service Architecture
**Flutter Cross-Platform Application** - Single codebase deployment targeting Android and iOS devices, with local SQLite storage and cloud sync capabilities.

### Testing Requirements
**Unit + Integration Testing** - Comprehensive testing including unit tests for business logic, integration tests for database operations, and UI testing for critical user flows.

### Additional Technical Assumptions and Requests
- **Authentication**: Google Sign-In API for user authentication (no separate account system)
- **Purchase Management**: Google Play Billing Library for in-app purchases with cross-device restoration
- **Data Storage**: Local SQLite database for offline functionality with Google Drive API v3 for cloud sync
- **Cloud Sync**: User's personal Google Drive app folder for data synchronization (user-owned data)
- **Offline Support**: Offline-first architecture with automatic sync when connectivity available
- **Platform**: Flutter cross-platform development using Dart for optimal performance across devices
- **Target SDK**: Flutter 3.16+ supporting modern Google Services integration via platform channels
- **Dependencies**: Google Services (Sign-In, Drive, Play Billing), Drift Database, Flutter Workmanager for sync

