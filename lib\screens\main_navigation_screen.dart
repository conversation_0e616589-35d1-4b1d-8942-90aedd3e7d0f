import 'package:flutter/material.dart';
import 'timeline_screen.dart';
import 'calendar_screen.dart';
import 'add_culture_screen.dart';
import 'library_screen.dart';
import 'settings_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      const TimelineScreen(),
      const CalendarScreen(),
      const AddCultureScreen(),
      const LibraryScreen(),
      const SettingsScreen(),
    ];
  }

  final List<NavigationDestination> _destinations = [
    const NavigationDestination(
      icon: Icon(Icons.timeline),
      selectedIcon: Icon(Icons.timeline),
      label: 'Timeline',
    ),
    const NavigationDestination(
      icon: Icon(Icons.calendar_today),
      selectedIcon: Icon(Icons.calendar_today),
      label: 'Calendar',
    ),
    const NavigationDestination(
      icon: Icon(Icons.add_circle_outline),
      selectedIcon: Icon(Icons.add_circle),
      label: 'Add',
    ),
    const NavigationDestination(
      icon: Icon(Icons.library_books_outlined),
      selectedIcon: Icon(Icons.library_books),
      label: 'Library',
    ),
    const NavigationDestination(
      icon: Icon(Icons.settings_outlined),
      selectedIcon: Icon(Icons.settings),
      label: 'Settings',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: NavigationBar(
        selectedIndex: _currentIndex,
        onDestinationSelected: _onDestinationSelected,
        destinations: _destinations,
      ),
    );
  }

  Future<void> _onDestinationSelected(int index) async {
    if (index == 2) { // Add Culture tab
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => const AddCultureScreen(),
        ),
      );

      // If culture was created successfully, switch to timeline tab
      if (result == true) {
        setState(() {
          _currentIndex = 0; // Switch to Timeline tab
        });
      }
    } else {
      setState(() {
        _currentIndex = index;
      });
    }
  }
}
