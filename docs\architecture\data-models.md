# Data Models

Based on the PRD requirements, I'll define the core business entities for CultureStack's offline-first architecture with cloud synchronization.

### Culture Entity
**Purpose:** Represents the root culture record with enhanced search and performance capabilities.

**Key Attributes:**
- id: String (UUID) - Unique identifier for sync across devices
- cultureId: String - Human-readable culture ID (C001, C002, etc.)
- species: String - Plant species/variety name **[Indexed for search]**
- explantType: String - Type of plant tissue used
- sourcePlantId: String? - Optional source plant identifier
- initiationDate: LocalDate - Date culture was started **[Indexed for date queries]**
- mediumComposition: String - Medium recipe or manual composition
- recipeId: String? - Reference to saved recipe if used
- initialConditions: String - Environmental conditions at initiation
- status: CultureStatus - Current lifecycle status **[Indexed for timeline queries]**
- isDeleted: Boolean - Soft delete flag for sync
- createdAt: Instant - Record creation timestamp
- updatedAt: Instant - Last modification timestamp **[Indexed for timeline sorting]**
- syncVersion: Long - Version for conflict resolution
- lastSyncedAt: Instant? - **[Added]** Last successful cloud sync
- deviceId: String - **[Added]** Device that last modified record

#### Dart Data Class Interface
```dart
@freezed
class Culture with _$Culture {
  const factory Culture({
    @Default(Uuid().v4()) String id,
    required String cultureId,
    required String species,
    required String explantType,
    String? sourcePlantId,
    required DateTime initiationDate,
    required String mediumComposition,
    String? recipeId,
    required String initialConditions,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Culture;

  factory Culture.fromJson(Map<String, dynamic> json) => _$CultureFromJson(json);
}

@JsonEnum()
enum CultureStatus {
  @JsonValue('HEALTHY') healthy,
  @JsonValue('CONTAMINATED') contaminated,
  @JsonValue('READY_FOR_TRANSFER') readyForTransfer,
  @JsonValue('IN_ROOTING') inRooting,
  @JsonValue('ACCLIMATIZING') acclimatizing,
  @JsonValue('COMPLETED') completed,
  @JsonValue('DISPOSED') disposed,
}
```

#### Relationships
- One-to-many with Subculture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Subculture Entity
**Purpose:** Represents cultures derived from parent cultures, maintaining lineage traceability through the culture family tree.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- subcultureId: String - Human-readable ID (S001, S002, etc.)
- parentCultureId: String - Reference to parent culture ID
- subcultureDate: LocalDate - Date of subculturing
- mediumComposition: String - Medium used for subculture
- recipeId: String? - Recipe reference if used
- explantCount: Int - Number of explants transferred
- status: CultureStatus - Current status
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last update timestamp
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that last modified record

#### Dart Data Class Interface
```dart
@freezed
class Subculture with _$Subculture {
  const factory Subculture({
    @Default(Uuid().v4()) String id,
    required String subcultureId,
    required String parentCultureId,
    required DateTime subcultureDate,
    required String mediumComposition,
    String? recipeId,
    required int explantCount,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Subculture;

  factory Subculture.fromJson(Map<String, dynamic> json) => _$SubcultureFromJson(json);
}
```

#### Relationships
- Many-to-one with Culture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Recipe Entity (Enhanced for Discovery)
**Purpose:** Stores standardized medium formulations with enhanced discoverability for user workflows.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- name: String - User-defined recipe name **[Indexed for search]**
- description: String? - Optional recipe description
- ingredients: List<Ingredient> - Structured ingredient list
- preparationNotes: String? - Special preparation instructions
- category: String? - Optional categorization
- **plantTypes: List<String> - [Added] Target plant categories for filtering**
- **difficultyLevel: DifficultyLevel - [Added] Beginner/Intermediate/Advanced**
- **tags: List<String> - [Added] Searchable tags for recipe discovery**
- usageCount: Int - Track recipe popularity **[Indexed for recommendations]**
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last modification
- version: Int - Recipe version for history tracking
- syncVersion: Long - Sync conflict resolution
- **lastSyncedAt: Instant? - [Added] Sync metadata**
- **deviceId: String - [Added] Last modification device**

#### Dart Data Class Interface
```dart
@freezed
class Recipe with _$Recipe {
  const factory Recipe({
    @Default(Uuid().v4()) String id,
    required String name,
    String? description,
    required List<Ingredient> ingredients,
    String? preparationNotes,
    String? category,
    @Default([]) List<String> plantTypes,
    @Default(DifficultyLevel.beginner) DifficultyLevel difficultyLevel,
    @Default([]) List<String> tags,
    @Default(0) int usageCount,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(DateTime.now) DateTime updatedAt,
    @Default(1) int version,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Recipe;

  factory Recipe.fromJson(Map<String, dynamic> json) => _$RecipeFromJson(json);
}

@freezed
class Ingredient with _$Ingredient {
  const factory Ingredient({
    required String name,
    required String concentration,
    required String unit,
  }) = _Ingredient;

  factory Ingredient.fromJson(Map<String, dynamic> json) => _$IngredientFromJson(json);
}

@JsonEnum()
enum DifficultyLevel {
  @JsonValue('BEGINNER') beginner,
  @JsonValue('INTERMEDIATE') intermediate,
  @JsonValue('ADVANCED') advanced,
}
```

#### Relationships
- One-to-many with Culture (recipes used in cultures)
- One-to-many with Subculture (recipes used in subcultures)

### Observation Entity
**Purpose:** Captures monitoring data and photos for culture health tracking and decision-making support.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- cultureId: String - Reference to culture or subculture
- observationDate: LocalDate - Date of observation
- contaminationStatus: Boolean - Contamination present
- survivalStatus: SurvivalStatus - Overall health assessment
- growthStage: GrowthStage - Current development stage
- notes: String? - Additional observations
- photoFilenames: List<String> - Local photo file references
- createdAt: Instant - Record creation
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that created record

#### Dart Data Class Interface
```dart
@freezed
class Observation with _$Observation {
  const factory Observation({
    @Default(Uuid().v4()) String id,
    required String cultureId,
    required DateTime observationDate,
    required bool contaminationStatus,
    required SurvivalStatus survivalStatus,
    required GrowthStage growthStage,
    String? notes,
    @Default([]) List<String> photoFilenames,
    @Default(false) bool isDeleted,
    @Default(DateTime.now) DateTime createdAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Observation;

  factory Observation.fromJson(Map<String, dynamic> json) => _$ObservationFromJson(json);
}

@JsonEnum()
enum SurvivalStatus {
  @JsonValue('EXCELLENT') excellent,
  @JsonValue('GOOD') good,
  @JsonValue('FAIR') fair,
  @JsonValue('POOR') poor,
}

@JsonEnum()
enum GrowthStage {
  @JsonValue('INITIATION') initiation,
  @JsonValue('ESTABLISHMENT') establishment,
  @JsonValue('GROWTH') growth,
  @JsonValue('READY_FOR_TRANSFER') readyForTransfer,
}
```

#### Relationships
- Many-to-one with Culture or Subculture
- One-to-many with Photo files (stored separately)

### Photo Entity (New - Based on Journey Analysis)
**Purpose:** Manages culture photos with efficient storage and sync capabilities.

```kotlin
data class Photo(
    val id: String = UUID.randomUUID().toString(),
    val filename: String,
    val observationId: String,
    val localPath: String,
    val cloudPath: String? = null,
    val thumbnailPath: String, // For performance in gallery views
    val compressionLevel: CompressionLevel,
    val uploadStatus: UploadStatus,
    val fileSize: Long,
    val capturedAt: Instant,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CompressionLevel { ORIGINAL, HIGH, MEDIUM, LOW }
enum class UploadStatus { PENDING, UPLOADING, COMPLETED, FAILED }
```

### BatchOperation Entity (New - For Bulk Workflows)
**Purpose:** Supports bulk operations like creating multiple subcultures from user journey requirements.

```kotlin
data class BatchOperation(
    val id: String = UUID.randomUUID().toString(),
    val operationType: BatchOperationType,
    val parentId: String,
    val targetCount: Int,
    val completedCount: Int = 0,
    val status: BatchStatus,
    val parameters: Map<String, String> = emptyMap(),
    val createdAt: Instant = Instant.now(),
    val completedAt: Instant? = null
)

enum class BatchOperationType {
    CREATE_SUBCULTURES, UPDATE_STATUS, BULK_OBSERVATION
}
enum class BatchStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### SyncQueue Entity (New - Based on Data Flow Analysis)
**Purpose:** Manages offline-to-cloud synchronization with priority handling and retry logic.

```kotlin
data class SyncQueue(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String, // "Culture", "Recipe", "Observation", "Photo"
    val entityId: String,
    val operation: SyncOperation, // CREATE, UPDATE, DELETE
    val priority: SyncPriority, // HIGH, NORMAL, LOW
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val lastAttempt: Instant? = null,
    val status: SyncStatus, // PENDING, IN_PROGRESS, COMPLETED, FAILED
    val errorMessage: String? = null,
    val createdAt: Instant = Instant.now(),
    val scheduledFor: Instant = Instant.now()
)

enum class SyncOperation { CREATE, UPDATE, DELETE }
enum class SyncPriority { HIGH, NORMAL, LOW }
enum class SyncStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### ConflictResolution Entity (New - For Multi-Device Conflicts)
**Purpose:** Handles sync conflicts when same entity is modified on multiple devices.

```kotlin
data class ConflictResolution(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String,
    val entityId: String,
    val localVersion: String, // JSON snapshot of local entity
    val cloudVersion: String, // JSON snapshot of cloud entity
    val localUpdatedAt: Instant,
    val cloudUpdatedAt: Instant,
    val resolutionStrategy: ConflictStrategy,
    val userChoice: ConflictChoice? = null,
    val resolvedAt: Instant? = null,
    val createdAt: Instant = Instant.now()
)

enum class ConflictStrategy { MANUAL, LAST_WRITE_WINS, MERGE }
enum class ConflictChoice { ACCEPT_LOCAL, ACCEPT_CLOUD, MERGE }
```

### Database Indexes (Performance Optimization)
```sql
-- Critical indexes based on user journey analysis
CREATE INDEX idx_culture_timeline ON cultures(status, updatedAt DESC, isDeleted);
CREATE INDEX idx_culture_search ON cultures(species, status) WHERE isDeleted = 0;
CREATE INDEX idx_culture_initiation_date ON cultures(initiationDate);
CREATE INDEX idx_recipe_discovery ON recipes(plantTypes, difficultyLevel, usageCount DESC);
CREATE INDEX idx_recipe_usage_count ON recipes(usageCount DESC);
CREATE INDEX idx_sync_queue_processing ON sync_queue(status, priority, scheduledFor);
CREATE INDEX idx_batch_operation_tracking ON batch_operations(status, createdAt);
CREATE INDEX idx_photo_sync ON photos(uploadStatus, createdAt);
CREATE INDEX idx_observation_timeline ON observations(cultureId, observationDate DESC);
CREATE INDEX idx_conflict_resolution ON conflict_resolutions(entityType, resolvedAt);
```

