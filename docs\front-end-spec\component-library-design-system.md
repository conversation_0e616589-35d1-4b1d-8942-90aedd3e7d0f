# Component Library / Design System

## Design System Approach
**Approach:** Create a custom design system tailored for scientific/lab applications with emphasis on data clarity, status indication, and touch-friendly interactions for sterile environment use.

## Core Components

### Culture Status Card
**Purpose:** Display culture health, progress, and required actions at-a-glance
**Variants:** List view (compact), Grid view (detailed), Dashboard widget
**States:** Healthy, Alert, Critical, Dormant, Completed
**Usage Guidelines:** Always include visual status indicator, next action date, and quick access to detail view

### Protocol Step Component
**Purpose:** Guide users through sequential culture procedures
**Variants:** Collapsed step, Expanded step, Active step, Completed step
**States:** Pending, In Progress, Completed, Skipped, Failed
**Usage Guidelines:** Clear progression indicators, embedded media support, timer integration

### Data Input Forms
**Purpose:** Streamlined data entry for observations and measurements
**Variants:** Quick entry (minimal fields), Detailed entry (comprehensive), Voice note integration
**States:** Empty, Validating, Valid, Error, Saved
**Usage Guidelines:** Smart defaults, offline capability, auto-save functionality
