import 'package:drift/drift.dart';
import 'package:json_annotation/json_annotation.dart';
import 'culture_status.dart';

part 'culture.g.dart';

/// Culture entity representing a tissue culture record
@JsonSerializable()
class Culture {
  const Culture({
    required this.id,
    required this.cultureId,
    required this.species,
    required this.explantType,
    this.sourcePlantId,
    required this.initiationDate,
    required this.mediumComposition,
    this.recipeId,
    this.initialConditions,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.syncedAt,
    this.isDeleted = false,
  });

  /// Unique identifier (UUID)
  final String id;

  /// Human-readable culture identifier
  final String cultureId;

  /// Plant species name
  final String species;

  /// Type of explant used
  final String explantType;

  /// Source plant identifier (optional)
  final String? sourcePlantId;

  /// Date when culture was initiated
  final DateTime initiationDate;

  /// Medium composition details
  final String mediumComposition;

  /// Recipe identifier (optional)
  final String? recipeId;

  /// Initial conditions notes (optional)
  final String? initialConditions;

  /// Current status of the culture
  final CultureStatus status;

  /// Record creation timestamp
  final DateTime createdAt;

  /// Last update timestamp
  final DateTime updatedAt;

  /// Last sync timestamp (optional)
  final DateTime? syncedAt;

  /// Soft delete flag
  final bool isDeleted;

  factory Culture.fromJson(Map<String, dynamic> json) => _$CultureFromJson(json);
  Map<String, dynamic> toJson() => _$CultureToJson(this);

  Culture copyWith({
    String? id,
    String? cultureId,
    String? species,
    String? explantType,
    String? sourcePlantId,
    DateTime? initiationDate,
    String? mediumComposition,
    String? recipeId,
    String? initialConditions,
    CultureStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? syncedAt,
    bool? isDeleted,
  }) {
    return Culture(
      id: id ?? this.id,
      cultureId: cultureId ?? this.cultureId,
      species: species ?? this.species,
      explantType: explantType ?? this.explantType,
      sourcePlantId: sourcePlantId ?? this.sourcePlantId,
      initiationDate: initiationDate ?? this.initiationDate,
      mediumComposition: mediumComposition ?? this.mediumComposition,
      recipeId: recipeId ?? this.recipeId,
      initialConditions: initialConditions ?? this.initialConditions,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}
