import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:drift/native.dart';
import 'package:drift/drift.dart';

import 'package:culture_stack/bloc/culture_list_cubit.dart';
import 'package:culture_stack/data/database/database.dart';
import 'package:culture_stack/data/models/culture_status.dart';

class MockCultureDatabase extends Mock implements CultureDatabase {}

void main() {
  group('CultureListCubit', () {
    late CultureDatabase database;
    late CultureListCubit cubit;

    setUp(() {
      // Use in-memory database for testing
      database = CultureDatabase.forTesting(NativeDatabase.memory());
    });

    tearDown(() {
      database.close();
    });

    test('initial state is CultureListState.initial', () {
      cubit = CultureListCubit(database);
      expect(cubit.state, const CultureListState.initial());
    });

    group('loadCultures', () {
      setUp(() {
        cubit = CultureListCubit(database);
      });

      tearDown(() {
        cubit.close();
      });

      blocTest<CultureListCubit, CultureListState>(
        'emits [loading, loaded] when cultures are loaded successfully',
        build: () => cubit,
        act: (cubit) => cubit.loadCultures(),
        expect: () => [
          const CultureListState.loading(),
          const CultureListState.loaded([]),
        ],
      );

      blocTest<CultureListCubit, CultureListState>(
        'emits [loading, loaded] with cultures sorted by updatedAt desc',
        build: () => cubit,
        setUp: () async {
          // Add test cultures
          final now = DateTime.now();
          final culture1 = CulturesCompanion.insert(
            id: 'test-1',
            cultureId: 'C001',
            species: 'Test Species 1',
            explantType: 'Leaf',
            initiationDate: now.subtract(const Duration(days: 5)),
            mediumComposition: 'MS',
            status: CultureStatus.healthy,
            createdAt: now.subtract(const Duration(days: 5)),
            updatedAt: now.subtract(const Duration(days: 2)),
          );
          final culture2 = CulturesCompanion.insert(
            id: 'test-2',
            cultureId: 'C002',
            species: 'Test Species 2',
            explantType: 'Stem',
            initiationDate: now.subtract(const Duration(days: 3)),
            mediumComposition: 'MS',
            status: CultureStatus.contaminated,
            createdAt: now.subtract(const Duration(days: 3)),
            updatedAt: now.subtract(const Duration(days: 1)),
          );

          await database.insertCulture(culture1);
          await database.insertCulture(culture2);
        },
        act: (cubit) => cubit.loadCultures(),
        verify: (cubit) {
          final state = cubit.state;
          state.whenOrNull(
            loaded: (cultures) {
              expect(cultures.length, 2);
              // Most recently updated should be first
              expect(cultures[0].cultureId, 'C002');
              expect(cultures[1].cultureId, 'C001');
            },
          );
        },
      );

      blocTest<CultureListCubit, CultureListState>(
        'filters out deleted cultures',
        build: () => cubit,
        setUp: () async {
          final now = DateTime.now();
          final activeCulture = CulturesCompanion.insert(
            id: 'active',
            cultureId: 'C001',
            species: 'Active Culture',
            explantType: 'Leaf',
            initiationDate: now,
            mediumComposition: 'MS',
            status: CultureStatus.healthy,
            createdAt: now,
            updatedAt: now,
            isDeleted: const Value(false),
          );
          final deletedCulture = CulturesCompanion.insert(
            id: 'deleted',
            cultureId: 'C002',
            species: 'Deleted Culture',
            explantType: 'Stem',
            initiationDate: now,
            mediumComposition: 'MS',
            status: CultureStatus.healthy,
            createdAt: now,
            updatedAt: now,
            isDeleted: const Value(true),
          );

          await database.insertCulture(activeCulture);
          await database.insertCulture(deletedCulture);
        },
        act: (cubit) => cubit.loadCultures(),
        verify: (cubit) {
          final state = cubit.state;
          state.whenOrNull(
            loaded: (cultures) {
              expect(cultures.length, 1);
              expect(cultures[0].cultureId, 'C001');
            },
          );
        },
      );
    });

    group('refreshCultures', () {
      setUp(() {
        cubit = CultureListCubit(database);
      });

      tearDown(() {
        cubit.close();
      });

      blocTest<CultureListCubit, CultureListState>(
        'emits loaded state with refreshed cultures',
        build: () => cubit,
        setUp: () async {
          final now = DateTime.now();
          final culture = CulturesCompanion.insert(
            id: 'test',
            cultureId: 'C001',
            species: 'Test Species',
            explantType: 'Leaf',
            initiationDate: now,
            mediumComposition: 'MS',
            status: CultureStatus.healthy,
            createdAt: now,
            updatedAt: now,
          );
          await database.insertCulture(culture);
        },
        act: (cubit) async => await cubit.refreshCultures(),
        verify: (cubit) {
          final state = cubit.state;
          state.whenOrNull(
            loaded: (cultures) {
              expect(cultures.length, 1);
              expect(cultures[0].cultureId, 'C001');
            },
          );
        },
      );
    });
  });
}