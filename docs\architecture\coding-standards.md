# Coding Standards

## Overview

This document defines the mandatory coding standards for CultureStack development. All code must adhere to these standards to ensure consistency, maintainability, and reliability across the Flutter application.

## Flutter Development Standards

### Code Structure (MANDATORY - ALWAYS enforce)

All files must follow this exact structure from top to bottom:

```dart
// 1. imports (dart: first, package: second, relative: last)
// 2. main exported widget/class
// 3. private subwidgets
// 4. helper functions
// 5. constants/static content
// 6. types/models
```

**Required imports order example:**
```dart
import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../models/culture.dart';
import '../services/culture_service.dart';
import 'culture_widget.dart';
```

### Naming Conventions (MANDATORY - ALWAYS enforce)

- **Classes**: `PascalCase` (e.g., `CultureManagementWidget`, `AuthenticationBloc`)
- **Functions/Methods**: `camelCase` starting with verb (e.g., `fetchCultures()`, `validateEmail()`)
- **Variables**: `camelCase` with auxiliary verbs (e.g., `isLoading`, `hasError`, `canSubmit`)
- **Files**: `snake_case` (e.g., `culture_management_widget.dart`, `auth_bloc.dart`)
- **Constants**: `lowerCamelCase` (e.g., `defaultPadding`, `maxRetryAttempts`)
- **Enums**: `PascalCase` with `@JsonValue(int)` for database storage

### Widget Patterns (MANDATORY - ALWAYS enforce)

```dart
// ALWAYS use const constructors when possible
class CultureWidget extends StatelessWidget {
  const CultureWidget({super.key, required this.culture});

  final Culture culture;

  @override
  Widget build(BuildContext context) =>
    // ALWAYS use arrow syntax for single expressions
    Container(
      child: Text(culture.species),
    ); // ALWAYS trailing comma
}

// ALWAYS create private widget classes, NEVER widget methods
class _CultureStatusIndicator extends StatelessWidget {
  const _CultureStatusIndicator({required this.status});

  final CultureStatus status;

  @override
  Widget build(BuildContext context) =>
    Icon(_getStatusIcon(status));
}
```

### State Management (MANDATORY - ALWAYS enforce)

**Use Cubit for simple state, Bloc for complex events:**

```dart
class CultureCubit extends Cubit<CultureState> {
  CultureCubit(this._cultureService) : super(const CultureState.initial());

  final CultureService _cultureService;

  Future<void> fetchCultures() async {
    emit(const CultureState.loading());
    try {
      final cultures = await _cultureService.getAllCultures();
      emit(CultureState.loaded(cultures));
    } catch (e) {
      emit(CultureState.error(e.toString()));
    }
  }
}

// ALWAYS use Freezed for state classes
@freezed
class CultureState with _$CultureState {
  const factory CultureState.initial() = _Initial;
  const factory CultureState.loading() = _Loading;
  const factory CultureState.loaded(List<Culture> cultures) = _Loaded;
  const factory CultureState.error(String message) = _Error;
}

// ALWAYS use BlocBuilder for UI state, BlocListener for side effects
BlocBuilder<CultureCubit, CultureState>(
  builder: (context, state) => state.when(
    initial: () => const SizedBox.shrink(),
    loading: () => const CircularProgressIndicator(),
    loaded: (cultures) => CultureListWidget(cultures: cultures),
    error: (message) => SelectableText.rich(
      TextSpan(
        text: 'Error: $message',
        style: TextStyle(color: Theme.of(context).colorScheme.error),
      ),
    ),
  ),
)
```

### Error Handling (MANDATORY - ALWAYS enforce)

```dart
// NEVER use SnackBars for errors, ALWAYS use SelectableText.rich
Widget buildError(String message) => SelectableText.rich(
  TextSpan(
    text: 'Error: $message',
    style: TextStyle(color: Theme.of(context).colorScheme.error),
  ),
);

// ALWAYS handle specific exceptions
try {
  await _cultureService.createCulture(cultureData);
} on ValidationException catch (e) {
  emit(CultureState.error('Invalid culture data: ${e.message}'));
} on NetworkException catch (e) {
  emit(CultureState.error('Network error: ${e.message}'));
} catch (e) {
  emit(CultureState.error('Unexpected error: $e'));
}
```

### Data Models (MANDATORY - ALWAYS enforce)

```dart
// ALWAYS include these fields in data models
@JsonSerializable(fieldRename: FieldRename.snake)
@freezed
class Culture with _$Culture {
  const factory Culture({
    required String id,
    required String cultureId,
    required String species,
    required String explantType,
    required DateTime initiationDate,
    required String mediumComposition,
    required String initialConditions,
    @Default(CultureStatus.healthy) CultureStatus status,
    @Default(false) bool isDeleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(1) int syncVersion,
    DateTime? lastSyncedAt,
    required String deviceId,
  }) = _Culture;

  factory Culture.fromJson(Map<String, dynamic> json) => _$CultureFromJson(json);
}

// ALWAYS use JsonEnum with int values for database storage
@JsonEnum()
enum CultureStatus {
  @JsonValue(0) healthy,
  @JsonValue(1) contaminated,
  @JsonValue(2) readyForTransfer,
  @JsonValue(3) completed,
}
```

### Performance Rules (MANDATORY - ALWAYS enforce)

- **ALWAYS** use `const` constructors where possible
- **ALWAYS** use `ListView.builder` for lists, NEVER `ListView(children: [])`
- **ALWAYS** use `AssetImage` for local images, `cached_network_image` for remote
- **ALWAYS** include `errorBuilder` in `Image.network`
- **ALWAYS** prefer `StatelessWidget` over `StatefulWidget`
- **ALWAYS** break deep widget trees into smaller components (max 3-4 levels)

### TextField Configuration (MANDATORY - ALWAYS enforce)

```dart
TextField(
  textCapitalization: TextCapitalization.words, // ALWAYS specify
  keyboardType: TextInputType.text,             // ALWAYS specify
  textInputAction: TextInputAction.next,        // ALWAYS specify
  decoration: InputDecoration(
    labelText: 'Culture Species',
    hintText: 'Enter the plant species name',
  ),
)
```

### Code Style (MANDATORY - ALWAYS enforce)

- **Line length**: Maximum 80 characters
- **Trailing commas**: ALWAYS add for multi-parameter functions/constructors
- **Arrow syntax**: ALWAYS use for single expressions (`=> expression`)
- **Debugging**: ALWAYS use `log()` from `dart:developer`, NEVER `print()`
- **Navigation**: ALWAYS use `GoRouter`, NEVER `Navigator.push`

### Theme Usage (MANDATORY - ALWAYS enforce)

```dart
// NEVER use deprecated theme properties
Text(
  'Culture Name',
  style: Theme.of(context).textTheme.titleLarge, // NOT headline6
)
Text(
  'Status',
  style: Theme.of(context).textTheme.bodyMedium, // NOT bodyText2
)
```

## Database Standards

### Drift Database Patterns

```dart
// ALWAYS include these columns in tables
@DataClassName('CultureData')
class Cultures extends Table {
  TextColumn get id => text()();
  TextColumn get cultureId => text()();
  TextColumn get species => text()();
  TextColumn get explantType => text()();
  DateTimeColumn get initiationDate => dateTime()();
  TextColumn get mediumComposition => text()();
  TextColumn get initialConditions => text()();
  IntColumn get status => intEnum<CultureStatus>()();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  IntColumn get syncVersion => integer().withDefault(const Constant(1))();
  DateTimeColumn get lastSyncedAt => dateTime().nullable()();
  TextColumn get deviceId => text()();

  @override
  Set<Column> get primaryKey => {id};
}

// ALWAYS create indexes for performance
class AppDatabase extends _$AppDatabase {
  @override
  List<Index> get indices => [
    // Timeline queries
    Index('idx_culture_timeline', [
      cultures.status,
      cultures.updatedAt,
    ]),
    // Search queries
    Index('idx_culture_search', [
      cultures.species,
      cultures.isDeleted,
    ]),
  ];
}
```

### Repository Pattern

```dart
abstract class CultureRepository {
  Stream<List<Culture>> watchActiveCultures();
  Future<List<Culture>> getCulturesByStatus(CultureStatus status);
  Future<Culture?> getCultureById(String id);
  Future<String> createCulture(Culture culture);
  Future<void> updateCulture(Culture culture);
  Future<void> deleteCulture(String id);
}

class LocalCultureRepository implements CultureRepository {
  const LocalCultureRepository(this._cultureDao);

  final CultureDao _cultureDao;

  @override
  Stream<List<Culture>> watchActiveCultures() {
    return _cultureDao.getAllActiveCultures()
        .map((list) => list.map(_mapToModel).toList());
  }

  Culture _mapToModel(CultureData data) => Culture(
    id: data.id,
    cultureId: data.cultureId,
    species: data.species,
    explantType: data.explantType,
    initiationDate: data.initiationDate,
    mediumComposition: data.mediumComposition,
    initialConditions: data.initialConditions,
    status: data.status,
    isDeleted: data.isDeleted,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
    syncVersion: data.syncVersion,
    lastSyncedAt: data.lastSyncedAt,
    deviceId: data.deviceId,
  );
}
```

## Testing Standards

### Unit Test Structure

```dart
group('CultureService', () {
  late CultureService cultureService;
  late MockCultureRepository mockRepository;

  setUp(() {
    mockRepository = MockCultureRepository();
    cultureService = CultureService(mockRepository);
  });

  group('createCulture', () {
    test('should create culture with valid data', () async {
      // Arrange
      const cultureData = Culture(
        id: 'test-id',
        cultureId: 'C001',
        species: 'Test Orchid',
        explantType: 'Leaf',
        initiationDate: DateTime(2025, 1, 1),
        mediumComposition: 'MS Medium',
        initialConditions: 'Sterile conditions',
        createdAt: DateTime(2025, 1, 1),
        updatedAt: DateTime(2025, 1, 1),
        deviceId: 'test-device',
      );

      when(() => mockRepository.createCulture(any()))
          .thenAnswer((_) async => 'test-id');

      // Act
      final result = await cultureService.createCulture(cultureData);

      // Assert
      expect(result, 'test-id');
      verify(() => mockRepository.createCulture(cultureData)).called(1);
    });
  });
});
```

### Widget Test Structure

```dart
testWidgets('CultureWidget displays culture information', (tester) async {
  // Arrange
  const culture = Culture(
    id: 'test-id',
    cultureId: 'C001',
    species: 'Test Orchid',
    explantType: 'Leaf',
    initiationDate: DateTime(2025, 1, 1),
    mediumComposition: 'MS Medium',
    initialConditions: 'Sterile conditions',
    createdAt: DateTime(2025, 1, 1),
    updatedAt: DateTime(2025, 1, 1),
    deviceId: 'test-device',
  );

  // Act
  await tester.pumpWidget(
    MaterialApp(
      home: CultureWidget(culture: culture),
    ),
  );

  // Assert
  expect(find.text('Test Orchid'), findsOneWidget);
  expect(find.text('C001'), findsOneWidget);
  expect(find.text('Leaf'), findsOneWidget);
});
```

## Code Generation Workflow

### After modifying any `@freezed` or `@JsonSerializable` classes:

```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

### After modifying database tables:

```bash
flutter pub run build_runner build --delete-conflicting-outputs
flutter test test/database_test.dart
```

## Pre-Commit Checklist

Before committing code, ensure:

- [ ] All widgets use `const` constructors where possible
- [ ] No `print()` statements (use `log()` from `dart:developer`)
- [ ] Trailing commas on multi-parameter functions
- [ ] Proper error handling with `SelectableText.rich`
- [ ] State management follows Bloc/Cubit patterns
- [ ] No deep widget nesting (max 3-4 levels)
- [ ] Build runner executed after model changes
- [ ] All tests pass: `flutter test`
- [ ] Code analysis passes: `flutter analyze`
- [ ] Format check passes: `dart format --set-exit-if-changed .`

## Common Anti-Patterns to Avoid

### ❌ Bad Practices

```dart
// Don't create widget methods
Widget _buildCultureCard() {
  return Card(...);
}

// Don't use print for debugging
print('Culture created: $culture');

// Don't use Navigator.push
Navigator.push(context, MaterialPageRoute(...));

// Don't use SnackBar for errors
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(content: Text('Error occurred')),
);
```

### ✅ Good Practices

```dart
// Create private widget classes
class _CultureCard extends StatelessWidget {
  const _CultureCard({required this.culture});
  final Culture culture;
  @override
  Widget build(BuildContext context) => Card(...);
}

// Use log for debugging
log('Culture created: $culture', name: 'CultureService');

// Use GoRouter for navigation
context.go('/culture/${culture.id}');

// Use SelectableText.rich for errors
SelectableText.rich(
  TextSpan(
    text: 'Error: $message',
    style: TextStyle(color: Theme.of(context).colorScheme.error),
  ),
)
```

## Violation Enforcement

**If any team member violates an "ALWAYS" rule, immediately point it out during code review so it can be corrected before merging.**

These standards are mandatory and non-negotiable for maintaining code quality and consistency across the CultureStack project.