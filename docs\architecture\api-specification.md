# API Specification

Based on the architecture analysis, CultureStack is a **native Android application** with **local-first storage** and **Google Drive integration** for cloud sync. Unlike traditional REST/GraphQL APIs, this application uses Google APIs and local data access patterns.

**API Architecture Decision:**
Since CultureStack is not a web-based fullstack application but rather a Flutter mobile app, the "API" layer consists of:
1. **Local Data Access APIs** (Drift database interfaces)
2. **Google Services API Integration** (Drive, Auth, Billing via platform channels)
3. **Internal Service Layer APIs** (Business logic interfaces)
4. **Platform Channel APIs** (Native platform integration)

### Local Data Access API (Room Database)

#### Culture Management API
```dart
@DriftAccessor(tables: [Cultures])
class CultureDao extends DatabaseAccessor<AppDatabase> with _$CultureDaoMixin {
  CultureDao(AppDatabase db) : super(db);

  Stream<List<CultureData>> getAllActiveCultures() {
    return (select(cultures)
          ..where((c) => c.isDeleted.equals(false))
          ..orderBy([(c) => OrderingTerm.desc(c.updatedAt)]))
        .watch();
  }

  Future<List<CultureData>> getCulturesByStatus(CultureStatus status) {
    return (select(cultures)
          ..where((c) => c.status.equals(status.name) & c.isDeleted.equals(false)))
        .get();
  }

  Future<List<CultureData>> searchCulturesBySpecies(String query) {
    return (select(cultures)
          ..where((c) => c.species.contains(query) & c.isDeleted.equals(false)))
        .get();
  }

  Future<int> insertCulture(CulturesCompanion culture) {
    return into(cultures).insert(culture);
  }

  Future<bool> updateCulture(CultureData culture) {
    return update(cultures).replace(culture);
  }

  Future<int> softDeleteCulture(String id, DateTime deletedAt) {
    return (update(cultures)
          ..where((c) => c.id.equals(id)))
        .write(CulturesCompanion(
          isDeleted: const Value(true),
          updatedAt: Value(deletedAt),
        ));
  }
}
```

#### Recipe Management API
```dart
@DriftAccessor(tables: [Recipes])
class RecipeDao extends DatabaseAccessor<AppDatabase> with _$RecipeDaoMixin {
  RecipeDao(AppDatabase db) : super(db);

  Stream<List<RecipeData>> getAllRecipes() {
    return (select(recipes)
          ..where((r) => r.isDeleted.equals(false))
          ..orderBy([(r) => OrderingTerm.desc(r.usageCount)]))
        .watch();
  }

  Future<List<RecipeData>> getRecipesByPlantType(String plantType) {
    return (select(recipes)
          ..where((r) => r.plantTypes.contains(plantType) & r.isDeleted.equals(false)))
        .get();
  }

  Future<List<RecipeData>> getRecipesByDifficulty(DifficultyLevel level) {
    return (select(recipes)
          ..where((r) => r.difficultyLevel.equals(level.name) & r.isDeleted.equals(false)))
        .get();
  }

  Future<int> insertRecipe(RecipesCompanion recipe) {
    return into(recipes).insert(recipe);
  }

  Future<int> incrementUsageCount(String id) {
    return (update(recipes)
          ..where((r) => r.id.equals(id)))
        .write(RecipesCompanion(
          usageCount: Value(recipes.usageCount + 1),
        ));
  }
}
```

### Google Services Integration API

#### Enhanced Google Drive Sync Service (Rate-Limited Implementation)
```dart
abstract class ConstraintAwareDriveService {
  Future<Result<SyncResult>> syncWithRateLimit(List<SyncOperation> operations);
  Future<Result<QuotaStatus>> estimateRemainingQuota();
  Future<Result<void>> scheduleDelayedSync(Duration delay);

  // Batch operations to respect API limits
  Future<Result<BatchSyncResult>> batchSyncCultures(List<Culture> cultures);
  Future<Result<PhotoSyncResult>> intelligentPhotoSync();

  // Graceful degradation
  Future<Result<void>> enableOfflineMode();
  Future<ServicesStatus> detectServiceAvailability();
}

class GoogleDriveService implements ConstraintAwareDriveService {
  final GoogleDriveApi _driveApi;
  final DriveApiRateLimiter _rateLimiter;
  final SyncPriorityQueue _priorityQueue;
  final QuotaTracker _quotaTracker;

  // Google Drive API Quotas (per user):
  // - 1,000 requests per 100 seconds
  // - 100 queries per second
  // - 750 MB uploads per day for free users
  static const int _maxRequestsPer100Seconds = 1000;
  static const int _maxQueriesPerSecond = 100;
  static const int _maxDailyUploadMB = 750;

  GoogleDriveService(this._driveApi, this._rateLimiter, this._priorityQueue, this._quotaTracker);

  @override
  Future<Result<SyncResult>> syncWithRateLimit(List<SyncOperation> operations) async {
    try {
      // Pre-sync quota check
      final quotaStatus = await _quotaTracker.getCurrentStatus();
      if (quotaStatus.isExhausted) {
        return Result.error(CultureStackError.quotaError(quotaStatus.resetTime));
      }

      // Priority-based operation sorting
      final prioritizedOps = _priorityQueue.sortByPriority(operations);
      final results = <OperationResult>[];
      var successCount = 0;
      var rateLimitHit = false;

      for (final operation in prioritizedOps) {
        // Check if we can proceed with this operation
        if (!await _rateLimiter.canProceed(operation.estimatedCost)) {
          // Queue remaining operations for later
          await _queueRemainingOperations(prioritizedOps.skip(results.length));
          rateLimitHit = true;
          break;
        }

        // Execute the operation
        final result = await _executeOperation(operation);
        results.add(result);

        if (result.success) {
          successCount++;
          _quotaTracker.recordSuccess(operation);
        } else {
          _quotaTracker.recordFailure(operation);

          // Handle rate limit response from server
          if (result.isRateLimitError) {
            await _handleRateLimitResponse(result);
            rateLimitHit = true;
            break;
          }
        }
      }

      return Result.success(SyncResult(
        totalOperations: operations.length,
        successfulOperations: successCount,
        failedOperations: results.length - successCount,
        rateLimitHit: rateLimitHit,
        nextAllowedSync: rateLimitHit ? _rateLimiter.nextAvailableTime : null,
      ));

    } catch (e) {
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }

  @override
  Future<Result<BatchSyncResult>> batchSyncCultures(List<Culture> cultures) async {
    // Intelligent batching based on quota availability
    final quotaStatus = await _quotaTracker.getCurrentStatus();
    final batchSize = _calculateOptimalBatchSize(quotaStatus, cultures.length);

    final batches = _splitIntoBatches(cultures, batchSize);
    var totalSuccess = 0;
    var totalFailed = 0;
    var rateLimitEncountered = false;

    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];

      // Create batch upload request (reduces API calls)
      final batchRequest = _createBatchRequest(batch);

      if (!await _rateLimiter.canProceed(batchRequest.estimatedCost)) {
        // Schedule remaining batches for later
        await _scheduleDelayedBatches(batches.skip(i), Duration(minutes: 2));
        rateLimitEncountered = true;
        break;
      }

      final result = await _executeBatchRequest(batchRequest);
      totalSuccess += result.successCount;
      totalFailed += result.failureCount;

      // Add delay between batches to respect rate limits
      if (i < batches.length - 1) {
        await Future.delayed(Duration(milliseconds: 200));
      }
    }

    return Result.success(BatchSyncResult(
      successCount: totalSuccess,
      failedCount: totalFailed,
      rateLimitHit: rateLimitEncountered,
      nextAllowedSync: rateLimitEncountered ? _rateLimiter.nextAvailableTime : null,
    ));
  }

  @override
  Future<Result<PhotoSyncResult>> intelligentPhotoSync() async {
    try {
      final pendingPhotos = await _getPendingPhotoUploads();
      final quotaStatus = await _quotaTracker.getCurrentStatus();

      // Calculate how many photos we can upload within quota
      final availableUploadMB = quotaStatus.remainingUploadQuotaMB;
      final photosToSync = _selectPhotosForSync(pendingPhotos, availableUploadMB);

      if (photosToSync.isEmpty) {
        return Result.success(PhotoSyncResult(
          photosUploaded: 0,
          quotaExhausted: true,
          nextUploadWindow: quotaStatus.resetTime,
        ));
      }

      var uploadedCount = 0;
      for (final photo in photosToSync) {
        if (!await _rateLimiter.canProceed(photo.estimatedApiCost)) {
          break;
        }

        final result = await _uploadPhoto(photo);
        if (result.success) {
          uploadedCount++;
          _quotaTracker.recordUpload(photo.compressedSizeMB);
        }

        // Progressive delay to avoid burst limit
        await Future.delayed(Duration(milliseconds: 500));
      }

      return Result.success(PhotoSyncResult(
        photosUploaded: uploadedCount,
        quotaExhausted: false,
        nextUploadWindow: null,
      ));

    } catch (e) {
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }

  int _calculateOptimalBatchSize(QuotaStatus quotaStatus, int totalItems) {
    final requestsAvailable = quotaStatus.requestsRemaining;
    final maxBatchSize = 100; // Google Drive batch limit

    // Conservative estimate: each culture sync = 2 API calls (metadata + data)
    final maxItemsForQuota = (requestsAvailable / 2).floor();

    return math.min(maxBatchSize, math.min(maxItemsForQuota, totalItems));
  }

  Future<void> _handleRateLimitResponse(OperationResult result) async {
    final retryAfter = result.retryAfterSeconds ?? 60;
    await _rateLimiter.recordRateLimitHit(Duration(seconds: retryAfter));

    // Notify user about rate limit with helpful message
    _notifyUserOfRateLimit(retryAfter);
  }

  void _notifyUserOfRateLimit(int retryAfterSeconds) {
    final message = retryAfterSeconds < 60
        ? "Sync paused briefly due to high activity. Retrying in ${retryAfterSeconds}s."
        : "Sync paused due to Google Drive limits. Retrying in ${(retryAfterSeconds / 60).ceil()} minutes.";

    // Emit notification event
    _eventBus.fire(SyncNotificationEvent(
      type: NotificationType.rateLimitWarning,
      message: message,
      retryAfter: Duration(seconds: retryAfterSeconds),
    ));
  }
}

class DriveApiRateLimiter {
  final TokenBucket _requestBucket;
  final TokenBucket _queryBucket;
  DateTime? _lastRateLimitHit;
  Duration? _rateLimitDuration;

  DriveApiRateLimiter()
    : _requestBucket = TokenBucket(1000, Duration(seconds: 100)), // 1000 requests per 100s
      _queryBucket = TokenBucket(100, Duration(seconds: 1));       // 100 queries per second

  Future<bool> canProceed(int estimatedCost) async {
    // Check if we're in rate limit cooldown
    if (_isInRateLimitCooldown()) {
      return false;
    }

    // Check both token buckets
    return _requestBucket.tryConsume(estimatedCost) &&
           _queryBucket.tryConsume(1);
  }

  Future<void> recordRateLimitHit(Duration duration) async {
    _lastRateLimitHit = DateTime.now();
    _rateLimitDuration = duration;

    // Reset token buckets to be conservative
    _requestBucket.reset();
    _queryBucket.reset();
  }

  bool _isInRateLimitCooldown() {
    if (_lastRateLimitHit == null || _rateLimitDuration == null) {
      return false;
    }

    final cooldownEnd = _lastRateLimitHit!.add(_rateLimitDuration!);
    return DateTime.now().isBefore(cooldownEnd);
  }

  DateTime? get nextAvailableTime {
    if (_isInRateLimitCooldown()) {
      return _lastRateLimitHit!.add(_rateLimitDuration!);
    }
    return null;
  }
}

class TokenBucket {
  final int capacity;
  final Duration refillPeriod;
  int _tokens;
  DateTime _lastRefill;

  TokenBucket(this.capacity, this.refillPeriod)
    : _tokens = capacity,
      _lastRefill = DateTime.now();

  bool tryConsume(int tokens) {
    _refillIfNeeded();

    if (_tokens >= tokens) {
      _tokens -= tokens;
      return true;
    }
    return false;
  }

  void _refillIfNeeded() {
    final now = DateTime.now();
    final timePassed = now.difference(_lastRefill);

    if (timePassed >= refillPeriod) {
      _tokens = capacity;
      _lastRefill = now;
    }
  }

  void reset() {
    _tokens = 0;
    _lastRefill = DateTime.now().add(refillPeriod);
  }
}

class QuotaTracker {
  int _requestsUsed = 0;
  int _uploadMBUsed = 0;
  DateTime _quotaPeriodStart = DateTime.now();

  static const Duration _quotaPeriod = Duration(hours: 24);

  QuotaStatus getCurrentStatus() {
    _resetIfNewPeriod();

    return QuotaStatus(
      requestsUsed: _requestsUsed,
      requestsRemaining: 1000 - _requestsUsed,
      resetTime: _quotaPeriodStart.add(_quotaPeriod),
      storageUsed: _uploadMBUsed,
      storageRemaining: 750 - _uploadMBUsed,
      recommendedSyncDelay: _calculateRecommendedDelay(),
    );
  }

  void recordSuccess(SyncOperation operation) {
    _requestsUsed += operation.estimatedCost;
  }

  void recordUpload(double sizeMB) {
    _uploadMBUsed += sizeMB.ceil();
  }

  Duration _calculateRecommendedDelay() {
    final quotaUsagePercent = _requestsUsed / 1000.0;

    if (quotaUsagePercent > 0.9) {
      return Duration(minutes: 10); // Very conservative near limit
    } else if (quotaUsagePercent > 0.7) {
      return Duration(minutes: 2);  // Moderate delay
    } else {
      return Duration(seconds: 30); // Minimal delay
    }
  }

  void _resetIfNewPeriod() {
    final now = DateTime.now();
    if (now.difference(_quotaPeriodStart) >= _quotaPeriod) {
      _requestsUsed = 0;
      _uploadMBUsed = 0;
      _quotaPeriodStart = now;
    }
  }

  bool get isExhausted => _requestsUsed >= 950 || _uploadMBUsed >= 700; // 95% threshold
}
```

@freezed
class QuotaStatus with _$QuotaStatus {
  const factory QuotaStatus({
    required int requestsUsed,
    required int requestsRemaining,
    required DateTime resetTime,
    required int storageUsed,
    required int storageRemaining,
    required Duration recommendedSyncDelay,
  }) = _QuotaStatus;

  factory QuotaStatus.fromJson(Map<String, dynamic> json) => _$QuotaStatusFromJson(json);
}

@freezed
class BatchSyncResult with _$BatchSyncResult {
  const factory BatchSyncResult({
    required int successCount,
    required int failedCount,
    required bool rateLimitHit,
    DateTime? nextAllowedSync,
  }) = _BatchSyncResult;

  factory BatchSyncResult.fromJson(Map<String, dynamic> json) => _$BatchSyncResultFromJson(json);
}
```

#### Authentication Service
```dart
abstract class AuthService {
  Future<Result<GoogleSignInAccount>> signInWithGoogle();
  Future<Result<void>> signOut();
  Future<GoogleSignInAccount?> getCurrentUser();
  Future<Result<void>> refreshTokenIfNeeded();
  bool isUserSignedIn();
}

class GoogleAuthService implements AuthService {
  final GoogleSignIn _googleSignIn;

  GoogleAuthService(this._googleSignIn);

  @override
  Future<Result<GoogleSignInAccount>> signInWithGoogle() async {
    try {
      final account = await _googleSignIn.signIn();
      if (account != null) {
        return Result.success(account);
      }
      return Result.error(AuthenticationCancelledException());
    } catch (e) {
      return Result.error(AuthenticationException(e.toString()));
    }
  }
}
```

#### Billing Service
```dart
abstract class BillingService {
  Future<Result<void>> initializeBilling();
  Future<Result<List<ProductDetails>>> querySubscriptions();
  Future<Result<PurchaseDetails>> purchaseSubscription(String productId);
  Future<Result<bool>> verifyPurchase(PurchaseDetails purchase);
  bool isPremiumUser();
}

class InAppPurchaseService implements BillingService {
  final InAppPurchase _inAppPurchase;

  InAppPurchaseService(this._inAppPurchase);

  @override
  Future<Result<void>> initializeBilling() async {
    final available = await _inAppPurchase.isAvailable();
    if (available) {
      return Result.success(null);
    }
    return Result.error(BillingUnavailableException());
  }
}
```

### Enhanced Offline Queue Service
```dart
abstract class OfflineQueueService {
  Future<Result<void>> queueOperation(SyncOperation operation, SyncPriority priority);
  Future<int> getQueueSize();
  Future<QueueStatus> getQueueStatus();
  Future<Result<SyncResult>> processQueueWhenOnline();
  Future<Result<void>> compactQueue();

  // Emergency capabilities
  Future<Result<String>> exportQueueForManualSync();
  Future<Result<void>> importQueueFromBackup(String data);
}

class SqfliteOfflineQueueService implements OfflineQueueService {
  final SyncQueueDao _syncQueueDao;

  SqfliteOfflineQueueService(this._syncQueueDao);

  @override
  Future<Result<void>> queueOperation(SyncOperation operation, SyncPriority priority) async {
    try {
      await _syncQueueDao.insertSyncOperation(operation, priority);
      return Result.success(null);
    } catch (e) {
      return Result.error(QueueOperationException(e.toString()));
    }
  }
}

@freezed
class QueueStatus with _$QueueStatus {
  const factory QueueStatus({
    required int pendingOperations,
    required Duration estimatedSyncTime,
    DateTime? lastSuccessfulSync,
    DateTime? oldestPendingOperation,
  }) = _QueueStatus;

  factory QueueStatus.fromJson(Map<String, dynamic> json) => _$QueueStatusFromJson(json);
}
```

### Graceful Degradation Service (Enhanced)
```dart
abstract class GracefulDegradationService {
  Future<ServicesStatus> detectGoogleServicesAvailability();
  Future<Result<void>> enableGuestMode();
  Future<Result<void>> enableOfflineOnlyMode();
  Future<Result<ExportResult>> exportLocalData();

  // Fallback authentication
  Future<Result<LocalAccount>> createLocalAccount(String username);
  Future<Result<MigrationResult>> migrateToGoogleAccount(LocalAccount localAccount);

  // Regional fallback strategies
  Future<Result<RegionalConfig>> detectRegionalConstraints();
  Future<Result<void>> enableRegionalFallbackMode(RegionalConfig config);
  Future<Result<AlternativeSync>> setupAlternativeSyncMethod();
}

class FlutterGracefulDegradationService implements GracefulDegradationService {
  final SharedPreferences _prefs;
  final DatabaseService _databaseService;
  final RegionalDetectionService _regionalService;

  FlutterGracefulDegradationService(this._prefs, this._databaseService, this._regionalService);

  @override
  Future<ServicesStatus> detectGoogleServicesAvailability() async {
    try {
      // Multi-tier detection strategy
      final playServicesAvailable = await _checkGooglePlayServices();
      final driveApiAccessible = await _testDriveApiAccess();
      final billingAvailable = await _checkPlayBilling();

      if (playServicesAvailable && driveApiAccessible && billingAvailable) {
        return ServicesStatus.available;
      } else if (playServicesAvailable) {
        return ServicesStatus.partial;
      } else {
        return ServicesStatus.unavailable;
      }
    } catch (e) {
      return ServicesStatus.unavailable;
    }
  }

  @override
  Future<Result<RegionalConfig>> detectRegionalConstraints() async {
    try {
      final region = await _regionalService.detectUserRegion();
      final constraints = _getRegionalConstraints(region);
      return Result.success(constraints);
    } catch (e) {
      return Result.error(CultureStackError.networkUnavailable());
    }
  }

  @override
  Future<Result<void>> enableRegionalFallbackMode(RegionalConfig config) async {
    try {
      // Configure app for regional limitations
      await _prefs.setBool('google_services_restricted', config.googleServicesBlocked);
      await _prefs.setBool('offline_only_mode', config.requiresOfflineOnly);
      await _prefs.setString('fallback_auth_method', config.fallbackAuthMethod);

      // Initialize alternative services if needed
      if (config.requiresAlternativeSync) {
        await setupAlternativeSyncMethod();
      }

      return Result.success(null);
    } catch (e) {
      return Result.error(CultureStackError.databaseError(e.toString()));
    }
  }

  @override
  Future<Result<AlternativeSync>> setupAlternativeSyncMethod() async {
    try {
      // Fallback 1: Local file export/import
      final localExportService = LocalFileExportService();
      await localExportService.initialize();

      // Fallback 2: QR code-based sync for small datasets
      final qrSyncService = QRCodeSyncService();
      await qrSyncService.initialize();

      return Result.success(AlternativeSync(
        exportService: localExportService,
        qrService: qrSyncService,
        method: SyncMethod.localFile,
      ));
    } catch (e) {
      return Result.error(CultureStackError.databaseError(e.toString()));
    }
  }

  RegionalConfig _getRegionalConstraints(String region) {
    final constraints = {
      'CN': RegionalConfig(
        region: 'China',
        googleServicesBlocked: true,
        requiresOfflineOnly: true,
        requiresAlternativeSync: true,
        fallbackAuthMethod: 'local_account',
        supportedFeatures: ['offline_tracking', 'local_export'],
      ),
      'IR': RegionalConfig(
        region: 'Iran',
        googleServicesBlocked: true,
        requiresOfflineOnly: true,
        requiresAlternativeSync: true,
        fallbackAuthMethod: 'local_account',
        supportedFeatures: ['offline_tracking', 'local_export'],
      ),
      'RU': RegionalConfig(
        region: 'Russia',
        googleServicesBlocked: false,
        requiresOfflineOnly: false,
        requiresAlternativeSync: true, // Backup option
        fallbackAuthMethod: 'google_or_local',
        supportedFeatures: ['limited_google_services', 'offline_tracking', 'local_export'],
      ),
    };

    return constraints[region] ?? RegionalConfig.unrestricted();
  }
}

@freezed
class RegionalConfig with _$RegionalConfig {
  const factory RegionalConfig({
    required String region,
    required bool googleServicesBlocked,
    required bool requiresOfflineOnly,
    required bool requiresAlternativeSync,
    required String fallbackAuthMethod,
    required List<String> supportedFeatures,
  }) = _RegionalConfig;

  factory RegionalConfig.unrestricted() => const RegionalConfig(
    region: 'Global',
    googleServicesBlocked: false,
    requiresOfflineOnly: false,
    requiresAlternativeSync: false,
    fallbackAuthMethod: 'google',
    supportedFeatures: ['full_functionality'],
  );

  factory RegionalConfig.fromJson(Map<String, dynamic> json) => _$RegionalConfigFromJson(json);
}

@freezed
class AlternativeSync with _$AlternativeSync {
  const factory AlternativeSync({
    required LocalFileExportService exportService,
    required QRCodeSyncService qrService,
    required SyncMethod method,
  }) = _AlternativeSync;
}

@JsonEnum()
enum SyncMethod {
  @JsonValue('GOOGLE_DRIVE') googleDrive,
  @JsonValue('LOCAL_FILE') localFile,
  @JsonValue('QR_CODE') qrCode,
  @JsonValue('MANUAL_EXPORT') manualExport,
}
```

@JsonEnum()
enum ServicesStatus {
  @JsonValue('AVAILABLE') available,
  @JsonValue('OFFLINE') offline,
  @JsonValue('NOT_AUTHENTICATED') notAuthenticated,
  @JsonValue('UNAVAILABLE') unavailable,
  @JsonValue('RATE_LIMITED') rateLimited,
  @JsonValue('QUOTA_EXCEEDED') quotaExceeded,
}

@freezed
class ExportResult with _$ExportResult {
  const factory ExportResult({
    required String filePath,
    required ExportFormat format, // JSON, CSV, PDF
    required bool includesPhotos,
    required int fileSize,
  }) = _ExportResult;

  factory ExportResult.fromJson(Map<String, dynamic> json) => _$ExportResultFromJson(json);
}

@JsonEnum()
enum ExportFormat {
  @JsonValue('JSON') json,
  @JsonValue('CSV') csv,
  @JsonValue('PDF') pdf,
}
```

### Internal Service Layer API

#### Culture Management Service
```dart
abstract class CultureService {
  Future<Result<Culture>> createCulture(CreateCultureRequest cultureRequest);
  Future<Result<Subculture>> createSubculture(CreateSubcultureRequest subcultureRequest);
  Future<Result<Observation>> addObservation(AddObservationRequest observationRequest);
  Future<Result<void>> updateCultureStatus(String cultureId, CultureStatus status);
  Future<Result<CultureLineage>> getCultureLineage(String cultureId);
  Future<Result<BatchOperation>> createBatchSubcultures(BatchSubcultureRequest batchRequest);
}

class CultureServiceImpl implements CultureService {
  final CultureDao _cultureDao;
  final SyncQueueService _syncQueueService;
  final EventBus _eventBus;

  CultureServiceImpl(this._cultureDao, this._syncQueueService, this._eventBus);

  @override
  Future<Result<Culture>> createCulture(CreateCultureRequest request) async {
    try {
      final culture = Culture(
        cultureId: request.cultureId,
        species: request.species,
        explantType: request.explantType,
        // ... other fields
      );

      await _cultureDao.insertCulture(culture.toCompanion());
      _eventBus.fire(CultureCreatedEvent(culture));
      await _syncQueueService.queueForSync(culture);

      return Result.success(culture);
    } catch (e) {
      return Result.error(CultureCreationException(e.toString()));
    }
  }
}
```

@freezed
class CreateCultureRequest with _$CreateCultureRequest {
  const factory CreateCultureRequest({
    required String species,
    required String explantType,
    String? sourcePlantId,
    required DateTime initiationDate,
    required String mediumComposition,
    String? recipeId,
    required String initialConditions,
  }) = _CreateCultureRequest;

  factory CreateCultureRequest.fromJson(Map<String, dynamic> json) => _$CreateCultureRequestFromJson(json);
}
```

### Error Handling (Enhanced with Constraints)
```dart
@freezed
class CultureStackError with _$CultureStackError implements Exception {
  const factory CultureStackError.networkUnavailable() = NetworkUnavailable;
  const factory CultureStackError.googleServicesUnavailable() = GoogleServicesUnavailable;
  const factory CultureStackError.storageQuotaExceeded() = StorageQuotaExceeded;
  const factory CultureStackError.rateLimitExceeded() = RateLimitExceeded; // New
  const factory CultureStackError.syncConflictDetected() = SyncConflictDetected;
  const factory CultureStackError.localStorageFull() = LocalStorageFull; // New
  const factory CultureStackError.databaseError(String cause) = DatabaseError;
  const factory CultureStackError.googleApiError(int code, String message) = GoogleApiError;
  const factory CultureStackError.quotaError(DateTime resetTime) = QuotaError; // New

  factory CultureStackError.fromJson(Map<String, dynamic> json) => _$CultureStackErrorFromJson(json);
}

// Result type for consistent error handling
@freezed
class Result<T> with _$Result<T> {
  const factory Result.success(T data) = Success<T>;
  const factory Result.error(CultureStackError error) = Error<T>;

  factory Result.fromJson(Map<String, dynamic> json, T Function(Object?) fromJsonT) => _$ResultFromJson(json, fromJsonT);
}
```

## Components

Based on the architectural patterns, tech stack, and constraint analysis, I'll define the major logical components across CultureStack's Flutter cross-platform architecture.

### Culture Management Component
**Responsibility:** Handles all culture and subculture lifecycle operations including creation, monitoring, status updates, and lineage tracking.

**Key Interfaces:**
- `CultureService.createCulture(CreateCultureRequest): Result<Culture>`
- `CultureService.createSubculture(CreateSubcultureRequest): Result<Subculture>`
- `CultureService.updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>`
- `CultureService.getCultureLineage(cultureId: String): Result<CultureLineage>`

**Dependencies:** Database Component, Sync Component, Notification Component

**Technology Stack:**
- Dart with async/await for async operations
- Drift database for local storage
- GetIt + Injectable for dependency injection
- Flutter Workmanager for background status updates

### Recipe Management Component
**Responsibility:** Manages recipe library operations including creation, search, categorization, and usage tracking for medium formulations.

**Key Interfaces:**
- `RecipeService.createRecipe(CreateRecipeRequest): Result<Recipe>`
- `RecipeService.searchRecipes(searchCriteria: RecipeSearchCriteria): Result<List<Recipe>>`
- `RecipeService.getRecipesByPlantType(plantType: String): Result<List<Recipe>>`
- `RecipeService.incrementUsageCount(recipeId: String): Result<Unit>`

**Dependencies:** Database Component, Search Component

**Technology Stack:**
- Drift database with custom FTS implementation
- json_annotation for structured ingredient data serialization
- Repository pattern for data access abstraction

### Observation & Photo Component
**Responsibility:** Handles observation logging, photo capture, compression, storage, and gallery management for culture documentation.

**Key Interfaces:**
- `ObservationService.addObservation(AddObservationRequest): Result<Observation>`
- `PhotoService.captureAndProcessPhoto(cultureId: String): Result<Photo>`
- `PhotoService.compressPhoto(photo: Photo, level: CompressionLevel): Result<OptimizedPhoto>`
- `PhotoService.getPhotoGallery(cultureId: String): Result<List<Photo>>`

**Dependencies:** Database Component, File Storage Component, Sync Component

**Technology Stack:**
- Flutter Camera plugin for photo capture
- cached_network_image for image loading and caching
- flutter_image_compress for optimal compression
- path_provider for cross-platform file management

### Sync Management Component
**Responsibility:** Orchestrates data synchronization between local SQLite storage and Google Drive, handling conflicts, rate limits, and offline scenarios.

**Key Interfaces:**
- `SyncService.queueForSync(entityType: String, entityId: String, operation: SyncOperation): Result<Unit>`
- `SyncService.performIntelligentSync(): Result<SyncResult>`
- `SyncService.resolveConflict(conflictId: String, resolution: ConflictChoice): Result<Unit>`
- `SyncService.enableOfflineMode(): Result<Unit>`

**Dependencies:** Google Services Component, Database Component, Queue Management Component

**Technology Stack:**
- googleapis package for Google Drive API v3
- Flutter Workmanager for background sync scheduling
- Dio for HTTP communication
- Rate limiting with custom Dart implementation

### Authentication & Billing Component
**Responsibility:** Manages Google account authentication, premium subscription verification, and user session state for freemium model support.

**Key Interfaces:**
- `AuthService.signInWithGoogle(): Result<GoogleSignInAccount>`
- `BillingService.purchaseSubscription(subscriptionId: String): Result<PurchaseResult>`
- `BillingService.verifyPremiumStatus(): Result<Boolean>`
- `AuthService.enableGuestMode(): Result<GuestAccount>`

**Dependencies:** Google Services Component, Database Component

**Technology Stack:**
- google_sign_in plugin for authentication
- in_app_purchase plugin for subscriptions
- shared_preferences for session persistence
- firebase_analytics for user behavior tracking

### Notification & Scheduling Component
**Responsibility:** Manages reminder scheduling, push notifications, calendar integration, and background task coordination for culture maintenance.

**Key Interfaces:**
- `NotificationService.scheduleReminder(reminderId: String, scheduledTime: Instant): Result<Unit>`
- `NotificationService.sendPushNotification(notification: CultureNotification): Result<Unit>`
- `SchedulingService.createRecurringReminder(reminder: RecurringReminder): Result<Unit>`
- `SchedulingService.getUpcomingTasks(timeRange: TimeRange): Result<List<ScheduledTask>>`

**Dependencies:** Database Component, Culture Management Component

**Technology Stack:**
- firebase_messaging for push notifications
- flutter_local_notifications for local scheduling
- Flutter Workmanager for background task execution
- Platform-specific notification channels via method channels

### Database Component
**Responsibility:** Provides unified data access layer with offline-first architecture, including local storage, caching, indexing, and transaction management.

**Key Interfaces:**
- `DatabaseService.executeInTransaction(block: suspend () -> Unit): Result<Unit>`
- `DatabaseService.getEntityById(entityType: String, id: String): Result<Any?>`
- `DatabaseService.performMaintenance(): Result<MaintenanceResult>`
- `DatabaseService.exportDatabase(format: ExportFormat): Result<String>`

**Dependencies:** None (foundational layer)

**Technology Stack:**
- SQLite with Drift ORM for local storage
- sqlcipher_flutter for database encryption
- Database migration strategies with Drift
- Optimized indexes for query performance

### Google Services Integration Component
**Responsibility:** Abstracts Google API interactions including Drive storage, authentication, billing, and service availability detection with graceful degradation.

**Key Interfaces:**
- `GoogleServicesManager.checkServiceAvailability(): ServicesStatus`
- `GoogleServicesManager.initializeServices(): Result<Unit>`
- `GoogleServicesManager.handleServiceUnavailable(): Result<DegradationStrategy>`
- `GoogleServicesManager.getQuotaStatus(): Result<QuotaStatus>`

**Dependencies:** None (external integration layer)

**Technology Stack:**
- Platform channels for Google Play Services integration
- googleapis package for Google Drive API v3
- google_sign_in plugin for authentication
- Custom availability checker via method channels

### Component Interaction Diagram (Optimized for Performance)
```mermaid
graph TB
    subgraph "Presentation Layer (Flutter Widgets)"
        A[Culture Screens]
        B[Recipe Screens]
        C[Calendar Screens]
    end

    subgraph "Business Logic Layer (Bloc/Cubit)"
        D[Culture Bloc]
        E[Recipe Bloc]
        F[Observation Bloc]
        G[Notification Cubit]
    end

    subgraph "Data Layer"
        H[Sync Management]
        I[Database Repository]
        J[Google Services Repository]
        K[Event Bus (Streams)]
    end

    A --> D
    B --> E
    C --> G
    D --> I
    E --> I
    F --> I
    G --> I

    H --> I
    H --> J

    D -.->|stream events| K
    F -.->|stream events| K
    E -.->|stream events| K
    K -.->|notifications| G
    K -.->|sync requests| H
    K -.->|state updates| D

    J -.->|circuit breaker| H
```

### Enhanced Component Architecture (Constraint-Aware)

#### Event-Driven Communication System (Flutter Streams)
```dart
abstract class ComponentEventBus {
  void publish(ComponentEvent event);
  Stream<T> subscribe<T extends ComponentEvent>();
  void publishAndForget(ComponentEvent event); // Fire-and-forget for performance
}

@freezed
class ComponentEvent with _$ComponentEvent {
  const factory ComponentEvent.cultureCreated(Culture culture) = CultureCreatedEvent;
  const factory ComponentEvent.observationAdded(Observation observation) = ObservationAddedEvent;
  const factory ComponentEvent.syncCompleted(String entityType, String entityId) = SyncCompletedEvent;
  const factory ComponentEvent.conflictDetected(ConflictResolution conflict) = ConflictDetectedEvent;

  factory ComponentEvent.fromJson(Map<String, dynamic> json) => _$ComponentEventFromJson(json);
}

class StreamEventBus implements ComponentEventBus {
  final StreamController<ComponentEvent> _controller = StreamController.broadcast();

  @override
  void publish(ComponentEvent event) {
    _controller.add(event);
  }

  @override
  Stream<T> subscribe<T extends ComponentEvent>() {
    return _controller.stream.where((event) => event is T).cast<T>();
  }

  @override
  void publishAndForget(ComponentEvent event) {
    _controller.add(event);
  }
}
```

#### Dependency Management with Circuit Breaker
```dart
class GoogleServicesCircuitBreaker {
  final int failureThreshold;
  final Duration timeout;

  int _failures = 0;
  DateTime? _lastFailureTime;
  CircuitState _state = CircuitState.closed;

  GoogleServicesCircuitBreaker({
    this.failureThreshold = 5,
    this.timeout = const Duration(seconds: 30),
  });

  Future<Result<T>> execute<T>(Future<T> Function() operation) async {
    switch (_state) {
      case CircuitState.open:
        if (_lastFailureTime != null &&
            DateTime.now().difference(_lastFailureTime!) > timeout) {
          _state = CircuitState.halfOpen;
          return _tryOperation(operation);
        } else {
          return Result.error(CultureStackError.googleServicesUnavailable());
        }
      case CircuitState.halfOpen:
      case CircuitState.closed:
        return _tryOperation(operation);
    }
  }

  Future<Result<T>> _tryOperation<T>(Future<T> Function() operation) async {
    try {
      final result = await operation();
      _onSuccess();
      return Result.success(result);
    } catch (e) {
      _onFailure();
      return Result.error(CultureStackError.googleApiError(0, e.toString()));
    }
  }
}

enum CircuitState { closed, open, halfOpen }
```

#### Resource Management Strategy
```dart
class ComponentResourceManager {
  // Flutter uses isolates for compute-intensive operations
  static const int _maxConcurrentPhotoOperations = 2;
  static const int _maxConcurrentSyncOperations = 3;

  final Semaphore _photoSemaphore = Semaphore(_maxConcurrentPhotoOperations);
  final Semaphore _syncSemaphore = Semaphore(_maxConcurrentSyncOperations);

  Future<T> executePhotoOperation<T>(Future<T> Function() operation) async {
    await _photoSemaphore.acquire();
    try {
      // Use compute for CPU-intensive photo operations
      return await compute(_executeInIsolate, operation);
    } finally {
      _photoSemaphore.release();
    }
  }

  Future<T> executeSyncOperation<T>(Future<T> Function() operation) async {
    await _syncSemaphore.acquire();
    try {
      return await operation();
    } finally {
      _syncSemaphore.release();
    }
  }

  ComponentPriority prioritizeComponents(SystemState systemState) {
    if (systemState.isLowMemory) return ComponentPriority.essentialOnly;
    if (systemState.isBatteryLow) return ComponentPriority.reduceBackground;
    if (systemState.isNetworkLimited) return ComponentPriority.offlineFocus;
    return ComponentPriority.fullFunctionality;
  }

  static Future<T> _executeInIsolate<T>(Future<T> Function() operation) async {
    return await operation();
  }
}

class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
```

#### Performance-Optimized Component Communication (Flutter Bloc)
```dart
class CultureBloc extends Bloc<CultureEvent, CultureState> {
  final CultureRepository _cultureRepository;
  final ComponentEventBus _eventBus;

  CultureBloc(this._cultureRepository, this._eventBus) : super(const CultureState.initial()) {
    on<CreateCultureEvent>(_onCreateCulture);
  }

  Future<void> _onCreateCulture(
    CreateCultureEvent event,
    Emitter<CultureState> emit,
  ) async {
    emit(const CultureState.loading());

    try {
      // Only essential synchronous operation
      final culture = await _cultureRepository.create(event.request);

      // Emit success state immediately
      emit(CultureState.success(culture));

      // All secondary operations are asynchronous (fire-and-forget)
      _eventBus.publishAndForget(ComponentEvent.cultureCreated(culture));

    } catch (e) {
      emit(CultureState.error(e.toString()));
    }
  }
}

@freezed
class CultureEvent with _$CultureEvent {
  const factory CultureEvent.createCulture(CreateCultureRequest request) = CreateCultureEvent;
  const factory CultureEvent.loadCultures() = LoadCulturesEvent;
  const factory CultureEvent.updateCulture(Culture culture) = UpdateCultureEvent;
}

@freezed
class CultureState with _$CultureState {
  const factory CultureState.initial() = CultureInitial;
  const factory CultureState.loading() = CultureLoading;
  const factory CultureState.success(Culture culture) = CultureSuccess;
  const factory CultureState.error(String message) = CultureError;
}
```

## External APIs

Based on CultureStack's native Android architecture and PRD requirements, the application integrates with several external APIs and services. Since this is a native mobile app rather than a traditional web application, "external APIs" refer to third-party service integrations.

### Google Drive API v3
**Purpose:** Primary cloud storage and synchronization service for user culture data, photos, and recipes.

- **Documentation:** https://developers.google.com/drive/api/v3/reference
- **Base URL(s):** https://www.googleapis.com/drive/v3/, https://www.googleapis.com/upload/drive/v3/
- **Authentication:** OAuth 2.0 with Google Sign-In API, Drive scope permissions
- **Rate Limits:** 1,000 requests per 100 seconds per user, 100 queries per second per user

**Key Endpoints Used:**
- `GET /files` - List user's culture data files in app folder
- `POST /files` - Create new culture data file or folder structure
- `PATCH /files/{fileId}` - Update existing culture records
- `POST /upload/files` - Upload culture photos with multipart encoding
- `GET /files/{fileId}` - Download culture data or photos for sync

**Integration Notes:**
- Uses app-specific folder scope for user privacy
- Implements exponential backoff for rate limiting
- Batch operations for efficient sync of multiple entities
- Conflict resolution based on file modification timestamps

### Google Sign-In API
**Purpose:** User authentication for premium features and Google Drive access verification.

- **Documentation:** https://developers.google.com/identity/sign-in/android
- **Base URL(s):** Integrated Android SDK, no direct HTTP calls
- **Authentication:** OAuth 2.0 flows with Google Play Services integration
- **Rate Limits:** No explicit rate limits, governed by Google Play Services

**Key Endpoints Used:**
- SDK method: `GoogleSignIn.getClient().signIn()` - Initiate sign-in flow
- SDK method: `GoogleSignIn.getLastSignedInAccount()` - Retrieve cached account
- SDK method: `GoogleSignIn.getClient().signOut()` - Sign out current user
- SDK method: `GoogleSignIn.requestPermissions()` - Request additional scopes

**Integration Notes:**
- Integrated with Google Play Services for seamless UX
- Handles scope incremental authorization for Drive access
- Automatic token refresh managed by SDK
- Graceful fallback to guest mode when unavailable

### Google Play Billing Library
**Purpose:** Premium subscription management and in-app purchase verification for freemium model.

- **Documentation:** https://developer.android.com/google/play/billing
- **Base URL(s):** Integrated Android library, communicates with Google Play servers
- **Authentication:** App signing key verification with Google Play Console
- **Rate Limits:** No explicit limits, managed by Google Play infrastructure

**Key Endpoints Used:**
- Library method: `BillingClient.queryPurchasesAsync()` - Verify active subscriptions
- Library method: `BillingClient.launchBillingFlow()` - Initiate subscription purchase
- Library method: `BillingClient.acknowledgePurchase()` - Acknowledge completed purchase
- Library method: `BillingClient.queryProductDetailsAsync()` - Get subscription pricing

**Integration Notes:**
- Server-side receipt verification for security
- Handles subscription restoration across devices
- Manages purchase state persistence for offline scenarios
- Real-time purchase updates via PurchasesUpdatedListener

### Firebase Cloud Messaging (FCM)
**Purpose:** Push notification delivery for culture reminders and sync notifications.

- **Documentation:** https://firebase.google.com/docs/cloud-messaging
- **Base URL(s):** https://fcm.googleapis.com/v1/projects/{project-id}/messages:send
- **Authentication:** Service account key for server-to-FCM communication
- **Rate Limits:** No published limits for downstream messages, reasonable use expected

**Key Endpoints Used:**
- `POST /v1/projects/{project-id}/messages:send` - Send targeted notification
- SDK method: `FirebaseMessaging.getToken()` - Retrieve device FCM token
- SDK method: `FirebaseMessaging.subscribeToTopic()` - Topic-based messaging
- SDK method: `FirebaseMessaging.setAutoInitEnabled()` - Enable/disable FCM

**Integration Notes:**
- Topic-based messaging for broadcast notifications (maintenance, updates)
- Device-specific tokens for personalized culture reminders
- Notification channels for Android 8.0+ compatibility
- Handles token refresh and registration updates

## Regional Constraints and Fallback Strategies

### Regional Availability Analysis
```kotlin
data class RegionalConstraint(
    val region: String,
    val googlePlayServices: ServiceStatus,
    val googleDriveAccess: ServiceStatus,
    val alternativeRequired: Boolean,
    val marketPenetration: Double
)

val regionalConstraints = listOf(
    RegionalConstraint("China", BLOCKED, BLOCKED, true, 0.20),
    RegionalConstraint("Iran", RESTRICTED, RESTRICTED, true, 0.02),
    RegionalConstraint("Russia", PARTIAL, PARTIAL, false, 0.05),
    RegionalConstraint("EU", AVAILABLE, GDPR_COMPLIANT, false, 0.15),
    RegionalConstraint("North America", AVAILABLE, AVAILABLE, false, 0.35),
    RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
)
// Impact: ~22% of potential users may have limited Google Services access
```

### Rate Limiting and Quota Management
```kotlin
class GoogleDriveRateLimiter {
    private val rateLimiter = RateLimiter.create(8.0) // 8 requests per 10 seconds
    private val burstAllowance = Semaphore(20) // Allow 20 burst requests

    suspend fun executeWithLimit(operation: suspend () -> Result<Any>): Result<Any> {
        return if (burstAllowance.tryAcquire()) {
            try {
                rateLimiter.acquire()
                operation()
            } finally {
                burstAllowance.release()
            }
        } else {
            operationQueue.add(DelayedOperation(operation, System.currentTimeMillis() + 60000))
            Result.failure(RateLimitExceeded(Duration.ofMinutes(1)))
        }
    }
}
```

### Constraint-Aware API Client
```kotlin
class ConstraintAwareApiClient {
    private val regionalLimiter = RegionalRateLimiter()
    private val quotaMonitor = ApiQuotaMonitor()

    suspend fun <T> executeApiCall(
        apiCall: suspend () -> T,
        region: String,
        priority: ApiCallPriority
    ): Result<T> {
        // Check regional constraints
        val regionalConstraint = regionalLimiter.getConstraints(region)
        if (regionalConstraint.blocked) {
            return Result.failure(RegionBlockedError(region))
        }

        // Check quota status
        val quotaStatus = quotaMonitor.getCurrentStatus()
        if (quotaStatus.approachingLimit && priority == LOW) {
            return Result.failure(QuotaThresholdError("Deferring low-priority call"))
        }

        // Execute with appropriate rate limiting
        return regionalLimiter.executeWithConstraints(regionalConstraint) {
            apiCall()
        }
    }
}
```

**External API Integration Summary:**
- **Google Services Ecosystem:** Leverages integrated Android SDKs for optimal UX
- **Rate Limiting:** Intelligent backoff and batching with burst allowance for API quotas
- **Regional Adaptation:** Fallback strategies for 22% of users with limited Google Services
- **Error Handling:** Comprehensive error recovery with graceful degradation
- **Privacy Compliance:** User-controlled data collection with GDPR compliance for EU
- **Offline Resilience:** Local fallbacks when external services unavailable
- **Cost Management:** Free tier optimization with scaling constraint awareness

## Core Workflows

Based on the PRD requirements and architectural components, I'll illustrate key system workflows using sequence diagrams that show the interactions between components, external APIs, and error handling paths.

### Culture Creation Workflow

This workflow demonstrates the complete process of creating a new culture, from user input through local storage and cloud synchronization.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant RM as Recipe Management
    participant DB as Database Component
    participant EB as Event Bus
    participant SM as Sync Management
    participant GD as Google Drive API

    User->>UI: Create new culture
    UI->>RM: Search for recipes
    RM->>DB: Query recipes by plant type
    DB-->>RM: Return matching recipes
    RM-->>UI: Recipe suggestions
    User->>UI: Select recipe & enter details
    UI->>CM: CreateCulture request

    CM->>DB: Insert culture record
    DB-->>CM: Culture created (local)
    CM->>EB: Publish CultureCreated event
    CM-->>UI: Success response
    UI-->>User: Culture created confirmation

    EB->>SM: Handle CultureCreated event
    SM->>GD: Upload culture metadata
    alt Sync Success
        GD-->>SM: Upload successful
        SM->>DB: Update sync status
    else Rate Limited
        GD-->>SM: Rate limit error
        SM->>SM: Queue for delayed sync
    else Network Error
        SM->>SM: Add to offline queue
    end
```

### Multi-Device Observation Sync Workflow

This sequence shows the complex interaction when observations are logged on one device and synchronized to another, including conflict resolution.

```mermaid
sequenceDiagram
    participant U1 as User Device A
    participant U2 as User Device B
    participant CM1 as Culture Mgmt A
    participant CM2 as Culture Mgmt B
    participant DB1 as Database A
    participant DB2 as Database B
    participant SM1 as Sync Mgmt A
    participant SM2 as Sync Mgmt B
    participant GD as Google Drive
    participant CR as Conflict Resolution

    U1->>CM1: Add contamination observation
    CM1->>DB1: Insert observation locally
    CM1->>CM1: Update culture status to CONTAMINATED
    CM1->>DB1: Update culture status
    CM1->>SM1: Queue sync operations

    SM1->>GD: Upload observation + culture update
    GD-->>SM1: Sync successful

    Note over U2: Meanwhile on Device B...
    U2->>CM2: View culture timeline
    CM2->>SM2: Check for remote updates
    SM2->>GD: Fetch latest changes
    GD-->>SM2: New observation + status update

    SM2->>DB2: Compare sync versions
    alt No Conflict
        SM2->>DB2: Apply updates
        SM2->>CM2: Notify of changes
        CM2-->>U2: Show updated status
    else Sync Conflict Detected
        SM2->>CR: Create conflict resolution
        CR->>CR: Analyze conflicts
        CR-->>SM2: Manual resolution required
        SM2->>CM2: Present conflict UI
        CM2-->>U2: User chooses resolution
        U2->>CM2: Select resolution strategy
        CM2->>CR: Apply resolution
        CR->>DB2: Update with merged data
        CR->>SM2: Resolution complete
        SM2->>GD: Upload resolved version
    end
```

### Bulk Subculture Creation Workflow

This demonstrates the batch operation pattern for creating multiple subcultures, including progress tracking and error recovery.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant BO as Batch Operation Service
    participant DB as Database Component
    participant SM as Sync Management
    participant NS as Notification Service

    User->>UI: Create 5 subcultures from Culture C001
    UI->>CM: BatchSubculture request
    CM->>BO: Create batch operation
    BO->>DB: Insert BatchOperation record
    BO-->>CM: Batch operation started
    CM-->>UI: Show progress indicator

    loop For each subculture (5 times)
        BO->>CM: Create individual subculture
        CM->>DB: Insert subculture record
        CM->>SM: Queue for sync
        BO->>BO: Increment completed count
        BO->>DB: Update batch progress
        BO->>UI: Progress update

        alt Subculture Creation Fails
            CM-->>BO: Creation error
            BO->>BO: Log error, continue with next
        end
    end

    BO->>DB: Mark batch as completed
    BO->>NS: Schedule reminders for new subcultures
    BO-->>UI: Batch operation complete
    UI-->>User: 5 subcultures created successfully

    Note over SM: Background sync of all new subcultures
    SM->>SM: Process sync queue
    loop For each queued subculture
        SM->>GD: Upload subculture data
        alt Sync Success
            GD-->>SM: Upload successful
        else Sync Failure
            SM->>SM: Retry with exponential backoff
        end
    end
```

## Data Flow Analysis and Optimization

### Critical Data Transformations in Workflows

#### Culture Creation Data Pipeline
```kotlin
// User Input → Domain Entity → Database Entity → Sync Payload
data class CultureDataFlow(
    val userInput: CultureCreationInput,           // Raw form data
    val domainEntity: CultureDomainEntity,         // Business logic applied
    val dbEntity: CultureDbEntity,                 // Persistence format
    val syncPayload: CultureSyncPayload            // Cloud format
)

// Data validation gates at each transformation
class CultureDataValidator {
    suspend fun validateTransformation(
        input: CultureCreationInput,
        output: CultureDomainEntity
    ): ValidationResult {
        val violations = mutableListOf<ValidationViolation>()

        if (input.species.isBlank()) violations.add(ValidationViolation.REQUIRED_FIELD_MISSING)
        if (input.selectedRecipeId != null && !recipeExists(input.selectedRecipeId)) {
            violations.add(ValidationViolation.INVALID_REFERENCE)
        }

        return ValidationResult(violations)
    }
}
```

#### Photo Processing Pipeline with Data Integrity
```kotlin
data class PhotoProcessingPipeline(
    val rawCapture: RawPhotoCapture,      // 8MB JPEG from camera
    val optimizedPhoto: OptimizedPhoto,    // 1.2MB WebP compressed
    val dbEntity: PhotoDbEntity,           // Database reference
    val syncPayload: DriveUploadPayload    // Cloud upload format
)

class PhotoDataIntegrityValidator {
    fun validatePhotoProcessing(pipeline: PhotoProcessingPipeline): IntegrityResult {
        val issues = mutableListOf<IntegrityIssue>()

        // Check file size reduction is reasonable (not corrupted)
        val compressionRatio = pipeline.optimizedPhoto.optimizedSize.toDouble() /
                               pipeline.rawCapture.fileSize
        if (compressionRatio > 0.8 || compressionRatio < 0.05) {
            issues.add(IntegrityIssue.SUSPICIOUS_COMPRESSION_RATIO)
        }

        // Verify file references are consistent
        if (!fileExists(pipeline.optimizedPhoto.optimizedPath)) {
            issues.add(IntegrityIssue.MISSING_OPTIMIZED_FILE)
        }

        return IntegrityResult(issues)
    }
}
```

### Data Flow Monitoring and Metrics
```kotlin
data class DataFlowMetrics(
    val transformationLatency: Duration,    // Time to process data through pipeline
    val dataLossRate: Double,              // Percentage of data lost in transformation
    val errorRate: Double,                 // Errors per transformation
    val throughput: Double,                // Records per second
    val bottleneckStage: PipelineStage     // Identified bottleneck location
)

class WorkflowDataFlowMonitor {
    fun monitorCultureCreationFlow(): Flow<DataFlowMetrics> {
        return combine(
            measureTransformationLatency(),
            measureDataLossRate(),
            measureErrorRate(),
            measureThroughput()
        ) { latency, lossRate, errorRate, throughput ->
            DataFlowMetrics(
                transformationLatency = latency,
                dataLossRate = lossRate,
                errorRate = errorRate,
                throughput = throughput,
                bottleneckStage = identifyBottleneck(latency, throughput)
            )
        }
    }
}
```

**Core Workflow Design Rationale:**
- **Event-Driven Architecture:** Workflows use event bus for loose coupling and better error isolation
- **Offline-First Design:** All workflows can operate without network connectivity, queuing sync operations
- **Graceful Degradation:** Each workflow has fallback paths for service unavailability
- **Progress Visibility:** Batch operations provide real-time progress feedback to users
- **Error Recovery:** Comprehensive error handling with retry logic and user notification
- **Data Integrity:** Validation gates at each transformation point prevent data corruption
- **Performance Monitoring:** Real-time metrics for bottleneck identification and optimization

## Database Schema

Based on the enhanced data models defined earlier, I'll create concrete database schema definitions using the database type selected in the Tech Stack (SQLite with Room).

### Core Entity Tables

#### Cultures Table
```sql
CREATE TABLE cultures (
    id TEXT PRIMARY KEY NOT NULL,
    culture_id TEXT NOT NULL UNIQUE,
    species TEXT NOT NULL,
    explant_type TEXT NOT NULL,
    source_plant_id TEXT,
    initiation_date INTEGER NOT NULL, -- Unix timestamp
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    initial_conditions TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Subcultures Table
```sql
CREATE TABLE subcultures (
    id TEXT PRIMARY KEY NOT NULL,
    subculture_id TEXT NOT NULL UNIQUE,
    parent_culture_id TEXT NOT NULL,
    subculture_date INTEGER NOT NULL,
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    explant_count INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (parent_culture_id) REFERENCES cultures (id),
    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Recipes Table
```sql
CREATE TABLE recipes (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    ingredients TEXT NOT NULL, -- JSON array of Ingredient objects
    preparation_notes TEXT,
    category TEXT,
    plant_types TEXT, -- JSON array of strings
    difficulty_level TEXT NOT NULL DEFAULT 'BEGINNER',
    tags TEXT, -- JSON array of strings
    usage_count INTEGER NOT NULL DEFAULT 0,
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL
);
```

### Enhanced Entity Tables

#### Photos Table
```sql
CREATE TABLE photos (
    id TEXT PRIMARY KEY NOT NULL,
    filename TEXT NOT NULL,
    observation_id TEXT NOT NULL,
    local_path TEXT NOT NULL,
    cloud_path TEXT,
    thumbnail_path TEXT NOT NULL,
    compression_level TEXT NOT NULL,
    upload_status TEXT NOT NULL DEFAULT 'PENDING',
    file_size INTEGER NOT NULL,
    captured_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (observation_id) REFERENCES observations (id)
);
```

#### Sync Queue Table
```sql
CREATE TABLE sync_queue (
    id TEXT PRIMARY KEY NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    operation TEXT NOT NULL, -- CREATE, UPDATE, DELETE
    priority TEXT NOT NULL DEFAULT 'NORMAL',
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    last_attempt INTEGER,
    status TEXT NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    created_at INTEGER NOT NULL,
    scheduled_for INTEGER NOT NULL
);
```

## Query Pattern Analysis and Optimization

### Critical Query Patterns and Performance

#### Culture Timeline Dashboard Query (Most Frequent)
```sql
-- Optimized two-phase loading for better performance
-- Phase 1: Load essential data (target: <20ms)
SELECT c.id, c.culture_id, c.species, c.status, c.updated_at
FROM cultures c
WHERE c.is_deleted = 0 AND c.status IN ('HEALTHY', 'READY_FOR_TRANSFER', 'IN_ROOTING')
ORDER BY c.updated_at DESC
LIMIT 50;

-- Phase 2: Async load details for visible items
SELECT r.name, COUNT(s.id) as subculture_count, MAX(o.observation_date) as last_observation
FROM cultures c
LEFT JOIN recipes r ON c.recipe_id = r.id
LEFT JOIN subcultures s ON c.id = s.parent_culture_id AND s.is_deleted = 0
LEFT JOIN observations o ON c.id = o.culture_id AND o.is_deleted = 0
WHERE c.id = ?
GROUP BY c.id;
```

#### Enhanced Search Performance with FTS
```sql
-- Full-text search index for recipe discovery
CREATE VIRTUAL TABLE recipe_fts USING fts5(
    recipe_id,
    name,
    description,
    plant_types,
    tags,
    ingredients
);

-- Optimized search query (target: <100ms for 1000+ recipes)
SELECT r.id, r.name, r.description, r.difficulty_level, r.usage_count
FROM recipe_fts fts
JOIN recipes r ON fts.recipe_id = r.id
WHERE recipe_fts MATCH 'orchid AND medium'
  AND r.difficulty_level = 'BEGINNER'
  AND r.is_deleted = 0
ORDER BY r.usage_count DESC
LIMIT 20;
```

### Performance Optimization Strategies

#### Materialized Views for Expensive Aggregations
```sql
-- Pre-computed culture summary for dashboard performance
CREATE TABLE culture_summary_cache (
    culture_id TEXT PRIMARY KEY,
    subculture_count INTEGER,
    observation_count INTEGER,
    photo_count INTEGER,
    last_observation_date INTEGER,
    contamination_detected INTEGER,
    cache_updated_at INTEGER,

    FOREIGN KEY (culture_id) REFERENCES cultures (id)
);
```

#### Critical Performance Indexes
```sql
-- Timeline queries (most frequent)
CREATE INDEX idx_culture_timeline
    ON cultures(status, updated_at DESC, is_deleted);

-- Recipe discovery queries
CREATE INDEX idx_recipe_discovery
    ON recipes(plant_types, difficulty_level, usage_count DESC);

-- Observation timeline per culture
CREATE INDEX idx_observation_timeline
    ON observations(culture_id, observation_date DESC);

-- Sync queue processing
CREATE INDEX idx_sync_queue_processing
    ON sync_queue(status, priority, scheduled_for);

-- Photo sync status queries
CREATE INDEX idx_photo_sync
    ON photos(upload_status, created_at);
```

### Query Performance Monitoring
```kotlin
data class QueryPerformanceMetrics(
    val queryType: String,
    val executionTimeMs: Long,
    val recordsScanned: Long,
    val recordsReturned: Long,
    val indexesUsed: List<String>,
    val optimizationRecommendations: List<String>
)

class DatabasePerformanceMonitor {
    fun analyzeSlowQueries(): Flow<QueryPerformanceAlert> {
        return queryExecutionTimes
            .filter { it.executionTime > Duration.ofMillis(100) }
            .map { slowQuery ->
                QueryPerformanceAlert(
                    query = slowQuery.sql,
                    executionTime = slowQuery.executionTime,
                    suggestedOptimizations = generateOptimizations(slowQuery)
                )
            }
    }
}
```

### Critical Performance Targets
- **Culture Timeline Load:** <50ms for 1000 cultures
- **Recipe Search:** <100ms for 1000+ recipes with full-text search
- **Photo Gallery:** <200ms for 50 photos with metadata
- **Sync Conflict Detection:** <50ms for 100 entities
- **Batch Operation Progress:** <25ms per progress update

**Database Schema Design Rationale:**
- **Offline-First:** All tables support offline operation with sync metadata
- **Performance Optimized:** Indexes and materialized views based on actual query patterns
- **Data Integrity:** Foreign key constraints and triggers enforce business rules
- **Sync-Aware:** Every entity includes sync versioning and conflict resolution support
- **Scalable:** Schema supports efficient queries up to 100K+ records per table
- **Query-Optimized:** Full-text search, covering indexes, and cursor-based pagination
- **Cache-Friendly:** Materialized views for expensive aggregations with trigger maintenance

## Coding Standards & Style Guide

### Flutter/Dart Coding Standards

#### **MANDATORY Code Structure (ALWAYS enforce)**
```dart
// File structure (top to bottom):
// 1. imports (dart: first, package: second, relative: last)
// 2. main exported widget
// 3. private subwidgets
// 4. helper functions
// 5. constants/static content
// 6. types/models

// Required imports order example:
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/user.dart';
```

#### **MANDATORY Naming Conventions (ALWAYS enforce)**
- **Classes**: `PascalCase` (e.g., `UserProfileWidget`, `AuthenticationBloc`)
- **Functions/Methods**: `camelCase` starting with verb (e.g., `fetchUser()`, `validateEmail()`)
- **Variables**: `camelCase` with auxiliary verbs (e.g., `isLoading`, `hasError`, `canSubmit`)
- **Files**: `snake_case` (e.g., `user_profile_widget.dart`, `auth_bloc.dart`)
- **Constants**: `lowerCamelCase` (e.g., `defaultPadding`, `maxRetryAttempts`)
- **Enums**: `PascalCase` with `@JsonValue(int)` for database storage

#### **MANDATORY Widget Patterns (ALWAYS enforce)**
```dart
// ALWAYS use const constructors when possible
class MyWidget extends StatelessWidget {
  const MyWidget({super.key, required this.data});
  final String data;

  @override
  Widget build(BuildContext context) =>
    // ALWAYS use arrow syntax for single expressions
    Container(child: Text(data)); // ALWAYS trailing comma
}

// ALWAYS create private widget classes, NEVER widget methods
class _PrivateSubWidget extends StatelessWidget {
  const _PrivateSubWidget({required this.value});
  final int value;
  @override
  Widget build(BuildContext context) => Text('$value');
}
```

#### **MANDATORY State Management (ALWAYS enforce)**
```dart
// Cubit for simple state, Bloc for complex events
class UserCubit extends Cubit<UserState> {
  UserCubit() : super(const UserState.initial());

  Future<void> fetchUser() async {
    emit(const UserState.loading());
    try {
      final user = await _repository.getUser();
      emit(UserState.loaded(user));
    } catch (e) {
      emit(UserState.error(e.toString()));
    }
  }
}

// ALWAYS use Freezed for state classes
@freezed
class UserState with _$UserState {
  const factory UserState.initial() = _Initial;
  const factory UserState.loading() = _Loading;
  const factory UserState.loaded(User user) = _Loaded;
  const factory UserState.error(String message) = _Error;
}
```

#### **MANDATORY Performance Rules (ALWAYS enforce)**
- **ALWAYS** use `const` constructors where possible
- **ALWAYS** use `ListView.builder` for lists, NEVER `ListView(children: [])`
- **ALWAYS** use `AssetImage` for local images, `cached_network_image` for remote
- **ALWAYS** include `errorBuilder` in `Image.network`
- **ALWAYS** prefer `StatelessWidget` over `StatefulWidget`
- **ALWAYS** break deep widget trees into smaller components (max 3-4 levels)

#### **MANDATORY Code Style (ALWAYS enforce)**
- **Line length**: Maximum 80 characters
- **Trailing commas**: ALWAYS add for multi-parameter functions/constructors
- **Arrow syntax**: ALWAYS use for single expressions (`=> expression`)
- **Debugging**: ALWAYS use `log()`, NEVER `print()`
- **Navigation**: ALWAYS use `GoRouter` or `auto_route`, NEVER `Navigator.push`

#### **Code Quality Checklist**
- [ ] All widgets use `const` constructors where possible
- [ ] No `print()` statements (use `log()`)
- [ ] Trailing commas on multi-parameter functions
- [ ] Proper error handling with `SelectableText.rich`
- [ ] State management follows Bloc/Cubit patterns
- [ ] Firebase queries are limited and indexed
- [ ] No deep widget nesting (max 3-4 levels)
- [ ] Build runner executed after model changes
- [ ] Navigation uses GoRouter, not Navigator
- [ ] TextField inputs properly configured

## Security Architecture

### Data Protection Strategy

#### **Encryption at Rest**
```dart
// SQLCipher integration for local database encryption
@DriftDatabase(
  tables: [Cultures, Recipes, Observations],
  daos: [CultureDao, RecipeDao, ObservationDao],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  static LazyDatabase _openConnection() {
    return LazyDatabase(() async {
      final dbFolder = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbFolder.path, 'culturestack_encrypted.db'));

      // Generate or retrieve encryption key
      final encryptionKey = await _getOrCreateEncryptionKey();

      return DatabaseConnection(
        NativeDatabase(
          file,
          setup: (database) {
            // Enable SQLCipher encryption
            database.execute("PRAGMA key = '$encryptionKey'");
            database.execute('PRAGMA cipher_page_size = 4096');
            database.execute('PRAGMA kdf_iter = 64000');
            database.execute('PRAGMA cipher_hmac_algorithm = HMAC_SHA512');
            database.execute('PRAGMA cipher_kdf_algorithm = PBKDF2_HMAC_SHA512');
          },
        ),
      );
    });
  }

  static Future<String> _getOrCreateEncryptionKey() async {
    const storage = FlutterSecureStorage();
    String? key = await storage.read(key: 'db_encryption_key');
    if (key == null) {
      // Generate new 256-bit key
      final random = Random.secure();
      final keyBytes = List<int>.generate(32, (i) => random.nextInt(256));
      key = base64Encode(keyBytes);
      await storage.write(key: 'db_encryption_key', value: key);
    }
    return key;
  }
}
```

#### **Authentication & Authorization**
```dart
class SecureGoogleAuthService {
  final GoogleSignIn _googleSignIn;

  SecureGoogleAuthService()
      : _googleSignIn = GoogleSignIn(
          scopes: [
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/userinfo.email',
          ],
          forceCodeForRefreshToken: true, // Force consent for security
        );

  Future<Result<SecureAuthResult>> signInSecurely() async {
    try {
      final account = await _googleSignIn.signIn();
      if (account == null) {
        return Result.error(AuthCancelledException());
      }

      // Validate token before proceeding
      final auth = await account.authentication;
      final isValid = await _validateToken(auth.accessToken, auth.idToken);

      if (!isValid) {
        await _googleSignIn.signOut();
        return Result.error(InvalidTokenException());
      }

      await _storeSecureSession(account, auth);
      return Result.success(SecureAuthResult(account: account));

    } catch (e) {
      return Result.error(AuthException(e.toString()));
    }
  }
}
```

### Privacy Protection

#### **PII Handling**
```dart
class PrivacyProtectionService {
  // Never log or store personally identifiable information
  static void logSecurely(String message, {Map<String, dynamic>? metadata}) {
    final sanitizedMetadata = metadata?.map((key, value) {
      if (_isSensitiveField(key)) {
        return MapEntry(key, '[REDACTED]');
      }
      return MapEntry(key, value);
    });

    log(message, metadata: sanitizedMetadata);
  }

  static bool _isSensitiveField(String field) {
    final sensitiveFields = {
      'email', 'user_id', 'access_token', 'refresh_token',
      'device_id', 'ip_address', 'location'
    };
    return sensitiveFields.contains(field.toLowerCase());
  }
}
```

## Deployment Architecture

### Build & Release Pipeline

#### **GitHub Actions Workflow**
```yaml
