// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subculture.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Subculture _$SubcultureFromJson(Map<String, dynamic> json) => Subculture(
      id: json['id'] as String,
      subcultureId: json['subcultureId'] as String,
      parentCultureId: json['parentCultureId'] as String,
      subcultureDate: DateTime.parse(json['subcultureDate'] as String),
      mediumComposition: json['mediumComposition'] as String,
      explantCount: (json['explantCount'] as num).toInt(),
      status: $enumDecode(_$CultureStatusEnumMap, json['status']),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      syncedAt: json['syncedAt'] == null
          ? null
          : DateTime.parse(json['syncedAt'] as String),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );

Map<String, dynamic> _$SubcultureToJson(Subculture instance) =>
    <String, dynamic>{
      'id': instance.id,
      'subcultureId': instance.subcultureId,
      'parentCultureId': instance.parentCultureId,
      'subcultureDate': instance.subcultureDate.toIso8601String(),
      'mediumComposition': instance.mediumComposition,
      'explantCount': instance.explantCount,
      'status': _$CultureStatusEnumMap[instance.status]!,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'syncedAt': instance.syncedAt?.toIso8601String(),
      'isDeleted': instance.isDeleted,
    };

const _$CultureStatusEnumMap = {
  CultureStatus.healthy: 0,
  CultureStatus.contaminated: 1,
  CultureStatus.readyForTransfer: 2,
  CultureStatus.inRooting: 3,
  CultureStatus.acclimatizing: 4,
  CultureStatus.completed: 5,
  CultureStatus.disposed: 6,
};
