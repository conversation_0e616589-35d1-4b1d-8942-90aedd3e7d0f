import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:drift/native.dart';

import 'package:culture_stack/bloc/culture_list_cubit.dart';
import 'package:culture_stack/data/database/database.dart';
import 'package:culture_stack/data/models/culture_status.dart';
import 'package:culture_stack/screens/timeline_screen.dart';

class MockCultureListCubit extends Mock implements CultureListCubit {}

void main() {
  group('TimelineScreen', () {
    late CultureDatabase database;
    late CultureListCubit cubit;

    setUp(() {
      database = CultureDatabase.forTesting(NativeDatabase.memory());
      cubit = CultureListCubit(database);
    });

    tearDown(() {
      cubit.close();
      database.close();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: BlocProvider<CultureListCubit>.value(
          value: cubit,
          child: const TimelineScreen(),
        ),
      );
    }

    testWidgets('displays loading indicator initially', (tester) async {
      await tester.pumpWidget(createTestWidget());
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays empty state when no cultures exist', (tester) async {
      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Welcome to CultureStack'), findsOneWidget);
      expect(find.text('No Cultures Yet'), findsOneWidget);
      expect(find.text('Start by creating your first culture using the Add tab below.'), findsOneWidget);
    });

    testWidgets('displays culture cards when cultures exist', (tester) async {
      // Add test culture to database
      final now = DateTime.now();
      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Arabidopsis thaliana',
        explantType: 'Leaf',
        initiationDate: now.subtract(const Duration(days: 10)),
        mediumComposition: 'MS',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check that culture card is displayed
      expect(find.text('C001'), findsOneWidget);
      expect(find.text('Arabidopsis thaliana'), findsOneWidget);
      expect(find.text('Explant: Leaf'), findsOneWidget);
      expect(find.textContaining('days ago'), findsOneWidget);
    });

    testWidgets('displays days since initiation correctly', (tester) async {
      final now = DateTime.now();
      final tenDaysAgo = now.subtract(const Duration(days: 10));

      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: tenDaysAgo,
        mediumComposition: 'MS',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.textContaining('10 days ago'), findsOneWidget);
    });

    testWidgets('displays status indicator with correct color for healthy status', (tester) async {
      final now = DateTime.now();
      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Healthy'), findsOneWidget);
    });

    testWidgets('displays status indicator with correct color for contaminated status', (tester) async {
      final now = DateTime.now();
      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS',
        status: CultureStatus.contaminated,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Contaminated'), findsOneWidget);
    });

    testWidgets('culture cards are tappable', (tester) async {
      final now = DateTime.now();
      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on culture card
      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      // Should show snackbar with coming soon message
      expect(find.textContaining('Coming soon!'), findsOneWidget);
    });

    testWidgets('contains RefreshIndicator widget', (tester) async {
      // Add test culture to database to show culture list
      final now = DateTime.now();
      final culture = CulturesCompanion.insert(
        id: 'test-1',
        cultureId: 'C001',
        species: 'Test Species',
        explantType: 'Leaf',
        initiationDate: now,
        mediumComposition: 'MS',
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );
      await database.insertCulture(culture);

      cubit.loadCultures();
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify RefreshIndicator is present
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });
  });
}