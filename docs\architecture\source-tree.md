# Source Tree Structure

## Overview

This document defines the mandatory project structure for CultureStack, a Flutter application following clean architecture principles with offline-first design and Google Services integration.

## Root Directory Structure

```
culturestack/
├── android/                    # Android platform-specific code
├── ios/                        # iOS platform-specific code (future)
├── lib/                        # Main Dart source code
├── test/                       # Unit and widget tests
├── integration_test/           # Integration tests
├── assets/                     # Static assets (images, fonts, etc.)
├── docs/                       # Project documentation
├── .bmad-core/                 # BMAD framework files
├── .trae/                      # Trae AI integration files
├── pubspec.yaml                # Flutter dependencies
├── pubspec.lock                # Locked dependency versions
├── analysis_options.yaml       # Dart analyzer configuration
├── README.md                   # Project overview
└── .gitignore                  # Git ignore rules
```

## lib/ Directory Structure (Feature-First Organization)

```
lib/
├── main.dart                           # Application entry point
├── app/                                # Application-level configuration
│   ├── app.dart                        # Main app widget
│   ├── theme/                          # App theming
│   │   ├── app_theme.dart
│   │   ├── color_scheme.dart
│   │   └── text_theme.dart
│   ├── router/                         # Navigation configuration
│   │   ├── app_router.dart
│   │   ├── route_paths.dart
│   │   └── route_guards.dart
│   └── config/                         # App configuration
│       ├── app_config.dart
│       ├── environment.dart
│       └── constants.dart
├── core/                               # Shared core functionality
│   ├── database/                       # Database configuration
│   │   ├── app_database.dart
│   │   ├── app_database.g.dart
│   │   ├── migrations/
│   │   └── database_provider.dart
│   ├── di/                             # Dependency injection
│   │   ├── injection.dart
│   │   ├── injection.config.dart
│   │   └── injection_modules.dart
│   ├── error/                          # Error handling
│   │   ├── exceptions.dart
│   │   ├── failures.dart
│   │   └── error_handler.dart
│   ├── network/                        # Network layer
│   │   ├── api_client.dart
│   │   ├── network_info.dart
│   │   └── rate_limiter.dart
│   ├── platform/                       # Platform channels
│   │   ├── google_services.dart
│   │   ├── device_info.dart
│   │   └── platform_channels.dart
│   ├── storage/                        # Local storage
│   │   ├── secure_storage.dart
│   │   ├── shared_preferences.dart
│   │   └── file_storage.dart
│   ├── sync/                           # Synchronization logic
│   │   ├── sync_manager.dart
│   │   ├── sync_queue.dart
│   │   ├── conflict_resolver.dart
│   │   └── offline_detector.dart
│   ├── utils/                          # Utility functions
│   │   ├── date_utils.dart
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── extensions.dart
│   └── widgets/                        # Shared widgets
│       ├── loading_indicator.dart
│       ├── error_display.dart
│       ├── empty_state.dart
│       └── custom_buttons.dart
├── features/                           # Feature modules
│   ├── authentication/                 # User authentication
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── auth_local_datasource.dart
│   │   │   │   └── auth_remote_datasource.dart
│   │   │   ├── models/
│   │   │   │   ├── user_model.dart
│   │   │   │   └── user_model.g.dart
│   │   │   └── repositories/
│   │   │       └── auth_repository_impl.dart
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   │   └── user.dart
│   │   │   ├── repositories/
│   │   │   │   └── auth_repository.dart
│   │   │   └── usecases/
│   │   │       ├── sign_in_with_google.dart
│   │   │       ├── sign_out.dart
│   │   │       └── get_current_user.dart
│   │   └── presentation/
│   │       ├── bloc/
│   │       │   ├── auth_bloc.dart
│   │       │   ├── auth_event.dart
│   │       │   ├── auth_state.dart
│   │       │   └── auth_bloc.freezed.dart
│   │       ├── pages/
│   │       │   ├── sign_in_page.dart
│   │       │   └── profile_page.dart
│   │       └── widgets/
│   │           ├── google_sign_in_button.dart
│   │           └── user_avatar.dart
│   ├── cultures/                       # Culture management
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── culture_local_datasource.dart
│   │   │   │   └── culture_remote_datasource.dart
│   │   │   ├── models/
│   │   │   │   ├── culture_model.dart
│   │   │   │   ├── culture_model.g.dart
│   │   │   │   └── culture_status.dart
│   │   │   └── repositories/
│   │   │       └── culture_repository_impl.dart
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   │   ├── culture.dart
│   │   │   │   └── culture.freezed.dart
│   │   │   ├── repositories/
│   │   │   │   └── culture_repository.dart
│   │   │   └── usecases/
│   │   │       ├── create_culture.dart
│   │   │       ├── get_cultures.dart
│   │   │       ├── update_culture.dart
│   │   │       └── delete_culture.dart
│   │   └── presentation/
│   │       ├── bloc/
│   │       │   ├── culture_list/
│   │       │   │   ├── culture_list_cubit.dart
│   │       │   │   ├── culture_list_state.dart
│   │       │   │   └── culture_list_cubit.freezed.dart
│   │       │   └── culture_form/
│   │       │       ├── culture_form_cubit.dart
│   │       │       ├── culture_form_state.dart
│   │       │       └── culture_form_cubit.freezed.dart
│   │       ├── pages/
│   │       │   ├── culture_list_page.dart
│   │       │   ├── culture_detail_page.dart
│   │       │   ├── culture_form_page.dart
│   │       │   └── culture_timeline_page.dart
│   │       └── widgets/
│   │           ├── culture_card.dart
│   │           ├── culture_status_indicator.dart
│   │           ├── culture_form.dart
│   │           └── culture_timeline.dart
│   ├── recipes/                        # Recipe management
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── recipe_local_datasource.dart
│   │   │   │   └── recipe_remote_datasource.dart
│   │   │   ├── models/
│   │   │   │   ├── recipe_model.dart
│   │   │   │   ├── recipe_model.g.dart
│   │   │   │   ├── ingredient_model.dart
│   │   │   │   └── difficulty_level.dart
│   │   │   └── repositories/
│   │   │       └── recipe_repository_impl.dart
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   │   ├── recipe.dart
│   │   │   │   ├── recipe.freezed.dart
│   │   │   │   ├── ingredient.dart
│   │   │   │   └── ingredient.freezed.dart
│   │   │   ├── repositories/
│   │   │   │   └── recipe_repository.dart
│   │   │   └── usecases/
│   │   │       ├── create_recipe.dart
│   │   │       ├── get_recipes.dart
│   │   │       ├── search_recipes.dart
│   │   │       └── increment_usage_count.dart
│   │   └── presentation/
│   │       ├── bloc/
│   │       │   ├── recipe_list/
│   │       │   ├── recipe_form/
│   │       │   └── recipe_search/
│   │       ├── pages/
│   │       │   ├── recipe_list_page.dart
│   │       │   ├── recipe_detail_page.dart
│   │       │   └── recipe_form_page.dart
│   │       └── widgets/
│   │           ├── recipe_card.dart
│   │           ├── ingredient_list.dart
│   │           └── recipe_search_bar.dart
│   ├── observations/                   # Observation tracking
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── bloc/
│   │       ├── pages/
│   │       └── widgets/
│   ├── photos/                         # Photo management
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── bloc/
│   │       ├── pages/
│   │       └── widgets/
│   ├── premium/                        # Premium features & billing
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   ├── models/
│   │   │   └── repositories/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── bloc/
│   │       ├── pages/
│   │       └── widgets/
│   └── settings/                       # App settings
│       ├── data/
│       │   ├── datasources/
│       │   ├── models/
│       │   └── repositories/
│       ├── domain/
│       │   ├── entities/
│       │   ├── repositories/
│       │   └── usecases/
│       └── presentation/
│           ├── bloc/
│           ├── pages/
│           └── widgets/
└── generated/                          # Generated code (build_runner)
    ├── assets.gen.dart
    └── l10n/
```

## Clean Architecture Layer Responsibilities

### Data Layer (`data/`)
- **Models**: Data transfer objects with JSON serialization
- **Datasources**: Abstract interfaces for data access (local/remote)
- **Repositories**: Implementation of domain repository contracts

### Domain Layer (`domain/`)
- **Entities**: Business objects with core business logic
- **Repositories**: Abstract contracts for data access
- **Usecases**: Single-responsibility business logic operations

### Presentation Layer (`presentation/`)
- **Bloc/Cubit**: State management for UI
- **Pages**: Screen-level widgets with routing
- **Widgets**: Reusable UI components

## Database Structure (Drift Tables)

```
lib/core/database/
├── app_database.dart               # Main database class
├── app_database.g.dart             # Generated database code
├── tables/
│   ├── cultures_table.dart         # Cultures table definition
│   ├── recipes_table.dart          # Recipes table definition
│   ├── observations_table.dart     # Observations table definition
│   ├── photos_table.dart           # Photos table definition
│   └── sync_queue_table.dart       # Sync queue table definition
├── daos/
│   ├── culture_dao.dart            # Culture data access object
│   ├── recipe_dao.dart             # Recipe data access object
│   ├── observation_dao.dart        # Observation data access object
│   └── sync_queue_dao.dart         # Sync queue data access object
└── migrations/
    ├── migration_v1_to_v2.dart     # Database migration scripts
    └── migration_v2_to_v3.dart
```

## Assets Directory Structure

```
assets/
├── images/                         # Image assets
│   ├── icons/                      # App icons and UI icons
│   │   ├── app_icon.png
│   │   ├── culture_status/         # Status indicator icons
│   │   └── navigation/             # Navigation icons
│   ├── illustrations/              # Onboarding and empty state images
│   │   ├── onboarding_1.png
│   │   ├── empty_cultures.png
│   │   └── error_state.png
│   └── logos/                      # Brand logos
│       ├── google_logo.png
│       └── app_logo.png
├── fonts/                          # Custom fonts (if any)
└── data/                           # Static data files
    ├── default_recipes.json        # Pre-loaded recipe data
    └── plant_species.json          # Plant species suggestions
```

## Test Directory Structure

```
test/
├── unit/                           # Unit tests
│   ├── core/
│   │   ├── database/
│   │   ├── network/
│   │   └── utils/
│   └── features/
│       ├── authentication/
│       │   ├── data/
│       │   ├── domain/
│       │   └── presentation/
│       ├── cultures/
│       └── recipes/
├── widget/                         # Widget tests
│   ├── features/
│   │   ├── cultures/
│   │   │   └── widgets/
│   │   └── recipes/
│   └── core/
│       └── widgets/
├── mocks/                          # Mock classes
│   ├── mock_repositories.dart
│   ├── mock_datasources.dart
│   └── mock_services.dart
├── fixtures/                       # Test data
│   ├── culture_fixtures.dart
│   ├── recipe_fixtures.dart
│   └── user_fixtures.dart
└── helpers/                        # Test helpers
    ├── test_database.dart
    ├── pump_app.dart
    └── mock_platform_channels.dart
```

## Integration Test Structure

```
integration_test/
├── app_test.dart                   # Main app flow tests
├── features/
│   ├── authentication_test.dart   # Auth flow tests
│   ├── culture_management_test.dart # Culture CRUD tests
│   ├── recipe_management_test.dart # Recipe CRUD tests
│   └── sync_test.dart             # Sync functionality tests
├── platforms/
│   ├── android_specific_test.dart # Android-specific tests
│   └── ios_specific_test.dart     # iOS-specific tests (future)
└── test_driver/
    └── integration_test.dart      # Test driver entry point
```

## Build Configuration

```
android/
├── app/
│   ├── build.gradle               # Android app build configuration
│   ├── src/
│   │   ├── main/
│   │   │   ├── AndroidManifest.xml # Android manifest
│   │   │   ├── kotlin/            # Platform-specific Kotlin code
│   │   │   └── res/               # Android resources
│   │   ├── debug/                 # Debug configuration
│   │   └── release/               # Release configuration
│   └── google-services.json       # Firebase configuration
├── build.gradle                   # Project-level build configuration
└── gradle.properties              # Gradle properties

ios/ (Future iOS Support)
├── Runner/
│   ├── Info.plist                 # iOS app configuration
│   ├── AppDelegate.swift          # iOS app delegate
│   └── Runner-Bridging-Header.h   # Objective-C bridging
├── Runner.xcodeproj/              # Xcode project
└── GoogleService-Info.plist       # Firebase configuration
```

## Documentation Structure

```
docs/
├── architecture/                   # Architecture documentation
│   ├── index.md                   # Architecture overview
│   ├── coding-standards.md        # This document
│   ├── source-tree.md             # This document
│   ├── tech-stack.md              # Technology choices
│   ├── data-models.md             # Database design
│   ├── api-specification.md       # API interfaces
│   ├── testing-strategy.md        # Testing approach
│   └── error-handling.md          # Error handling patterns
├── development/                    # Development guides
│   ├── setup.md                   # Development environment setup
│   ├── build-and-deploy.md        # Build and deployment guide
│   └── contributing.md            # Contribution guidelines
├── user/                          # User documentation
│   ├── user-guide.md              # End-user guide
│   └── troubleshooting.md         # Common issues and solutions
└── api/                           # API documentation
    ├── local-api.md               # Local database API
    └── google-services.md         # Google Services integration
```

## File Naming Conventions

### Dart Files
- **Classes**: `snake_case.dart` (e.g., `culture_management_page.dart`)
- **Features**: `feature_purpose.dart` (e.g., `culture_list_cubit.dart`)
- **Barrel exports**: `index.dart` or feature name (e.g., `cultures.dart`)

### Test Files
- **Unit tests**: `{class_name}_test.dart` (e.g., `culture_service_test.dart`)
- **Widget tests**: `{widget_name}_test.dart` (e.g., `culture_card_test.dart`)
- **Integration tests**: `{feature_name}_test.dart` (e.g., `culture_flow_test.dart`)

### Asset Files
- **Images**: `descriptive_name.png` (e.g., `culture_healthy_icon.png`)
- **Data files**: `descriptive_name.json` (e.g., `default_recipes.json`)

## Import Organization

### Feature Imports
```dart
// Dart SDK imports
import 'dart:async';
import 'dart:convert';

// Flutter framework imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Third-party package imports
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get_it/get_it.dart';

// App core imports
import '../../core/database/app_database.dart';
import '../../core/di/injection.dart';
import '../../core/error/exceptions.dart';

// Feature imports (same feature)
import '../domain/entities/culture.dart';
import '../domain/repositories/culture_repository.dart';

// Feature imports (other features)
import '../../recipes/domain/entities/recipe.dart';
import '../../authentication/domain/entities/user.dart';
```

## Generated Code Management

### Build Runner Files
- **Database**: `*.g.dart` files generated by Drift
- **JSON**: `*.g.dart` files generated by json_annotation
- **Freezed**: `*.freezed.dart` files generated by Freezed
- **Injectable**: `*.config.dart` files generated by Injectable

### Regeneration Commands
```bash
# Generate all
flutter pub run build_runner build --delete-conflicting-outputs

# Watch for changes
flutter pub run build_runner watch --delete-conflicting-outputs

# Clean generated files
flutter pub run build_runner clean
```

## Version Control Ignore Rules

### .gitignore Key Entries
```gitignore
# Generated files
*.g.dart
*.freezed.dart
*.config.dart

# Build artifacts
build/
.dart_tool/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Environment
.env
.env.local

# Platform specific
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
```

This source tree structure ensures:
- **Clear separation of concerns** with clean architecture layers
- **Feature-first organization** for scalability
- **Consistent naming conventions** for maintainability
- **Proper test organization** for comprehensive coverage
- **Platform-specific code isolation** for cross-platform support
- **Generated code management** for build automation