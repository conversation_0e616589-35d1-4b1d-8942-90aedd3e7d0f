# Tech Stack

This is the **DEFINITIVE** technology selection for the entire CultureStack project. All development must use these exact versions and technologies.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale & Constraints |
|----------|------------|---------|---------|-------------------------|
| Mobile Platform | Flutter | 3.16+ | Cross-platform mobile framework | PRD requirement + future iOS; **Constraint:** Platform channel complexity |
| Programming Language | Dart | 3.2+ | Primary development language | Flutter-native language; **Constraint:** Requires developer Dart expertise |
| UI Framework | Flutter Widgets | Built-in | Declarative UI framework | Native Flutter approach; **Constraint:** Custom platform styling needed |
| Database | SQLite + Drift | Drift 2.14+ | Local data storage and ORM | Offline-first; **Constraint:** Performance degrades >100K records |
| Database Encryption | SQLCipher | Via drift | Database encryption for sensitive data | **Added:** Security for culture data on compromised devices |
| Authentication | Google Sign-In (Flutter) | google_sign_in 6.1+ | User authentication for premium features | PRD requirement; **Constraint:** Platform channel dependency |
| Fallback Auth | Guest Mode | Custom | Local-only access without Google account | **Added:** Graceful degradation for Google Services limitations |
| Cloud Storage | Google Drive API | googleapis 11.0+ | User data synchronization | PRD requirement; **Constraint:** 15GB storage limit, rate limits |
| Billing | Flutter In-App Purchase | in_app_purchase 3.1+ | Premium subscription management | PRD requirement; **Constraint:** Platform-specific implementations |
| Image Handling | Cached Network Image | cached_network_image 3.3+ | Async image loading and caching | Photo management; **Constraint:** Memory usage for large galleries |
| Image Compression | Flutter Image Compress | flutter_image_compress 2.0+ | Automatic photo compression | **Added:** Mitigate storage constraints |
| Networking | Dio + HTTP | dio 5.3+ | HTTP client for API calls | **Constraint:** Google Drive API rate limiting |
| Serialization | json_annotation | json_annotation 4.8+ | JSON serialization for sync | Type-safe; excellent Drift compatibility |
| Background Tasks | Flutter Workmanager | workmanager 0.5+ | Sync scheduling and notifications | Flutter recommended; handles Google Drive rate limits |
| Dependency Injection | Get It + Injectable | get_it 7.6+ / injectable 2.3+ | Dependency management | Flutter-recommended DI pattern |
| Testing Framework | Flutter Test + Integration Test | Built-in | Comprehensive cross-platform testing | Multi-layer testing with platform channel mocking |
| Build System | Flutter Build | flutter 3.16+ | Build automation | Flutter standard; supports multi-package architecture |
| Version Control | Git | 2.40+ | Source code management | Industry standard |
| State Management | Flutter Bloc | flutter_bloc 8.1+ | Predictable state management | Flutter best practice; **Constraint:** Learning curve |
| Code Generation | Build Runner | build_runner 2.4+ | Code generation for serialization | Dart ecosystem standard |
| Push Notifications | Firebase Cloud Messaging | firebase_messaging 14.7+ | Reminder notifications | **Constraint:** Platform-specific setup |
| Analytics | Firebase Analytics | firebase_analytics 10.7+ | Usage tracking and insights | Privacy-compliant; **Constraint:** Google Services dependency |
| Crash Reporting | Firebase Crashlytics | firebase_crashlytics 3.4+ | Error monitoring | Production stability; **Constraint:** Google Services dependency |

### Additional Architecture Components (Based on Constraint Analysis):

- **Storage Monitoring Service:** Tracks Google Drive usage and warns users before limits
- **Conflict Resolution Engine:** Handles sync conflicts with user-friendly resolution UI
- **Graceful Degradation Manager:** Provides offline-only functionality when Google Services unavailable
- **Data Archiving System:** Manages culture record lifecycle to prevent database performance issues

