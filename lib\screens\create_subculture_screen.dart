import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';

import '../bloc/create_subculture_cubit.dart';
import '../data/database/database.dart';
import '../data/models/culture_status.dart';

class CreateSubcultureScreen extends StatelessWidget {
  const CreateSubcultureScreen({super.key, required this.parentCulture});

  final Culture parentCulture;

  @override
  Widget build(BuildContext context) => BlocProvider(
    create: (context) => CreateSubcultureCubit(CultureDatabase()),
    child: _CreateSubcultureView(parentCulture: parentCulture),
  );
}

class _CreateSubcultureView extends StatelessWidget {
  const _CreateSubcultureView({required this.parentCulture});

  final Culture parentCulture;

  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(
      title: const Text('Create Subculture'),
      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
    ),
    body: BlocListener<CreateSubcultureCubit, CreateSubcultureState>(
      listener: (context, state) {
        state.whenOrNull(
          success: () {
            log('Subculture created successfully', name: 'CreateSubcultureScreen');
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Subculture created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          },
          error: (message) {
            log('Error creating subculture: $message', name: 'CreateSubcultureScreen');
          },
        );
      },
      child: BlocBuilder<CreateSubcultureCubit, CreateSubcultureState>(
        builder: (context, state) => state.when(
          initial: () => _CreateSubcultureForm(parentCulture: parentCulture),
          loading: () => const Center(child: CircularProgressIndicator()),
          success: () => _CreateSubcultureForm(parentCulture: parentCulture),
          error: (message) => Column(
            children: [
              Expanded(
                child: _CreateSubcultureForm(parentCulture: parentCulture),
              ),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                color: Theme.of(context).colorScheme.errorContainer,
                child: SelectableText.rich(
                  TextSpan(
                    text: 'Error: $message',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

class _CreateSubcultureForm extends StatefulWidget {
  const _CreateSubcultureForm({required this.parentCulture});

  final Culture parentCulture;

  @override
  State<_CreateSubcultureForm> createState() => _CreateSubcultureFormState();
}

class _CreateSubcultureFormState extends State<_CreateSubcultureForm> {
  final _formKey = GlobalKey<FormState>();
  final _subcultureDateController = TextEditingController();
  final _mediumCompositionController = TextEditingController();
  final _explantCountController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _subcultureDateController.text = _formatDate(_selectedDate);
    // Pre-fill medium composition from parent culture
    _mediumCompositionController.text = widget.parentCulture.mediumComposition;
  }

  @override
  void dispose() {
    _subcultureDateController.dispose();
    _mediumCompositionController.dispose();
    _explantCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => SingleChildScrollView(
    padding: const EdgeInsets.all(16.0),
    child: Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ParentCultureInfo(culture: widget.parentCulture),
          const SizedBox(height: 24),
          _SubcultureDateField(
            controller: _subcultureDateController,
            selectedDate: _selectedDate,
            onDateChanged: (date) {
              setState(() {
                _selectedDate = date;
                _subcultureDateController.text = _formatDate(date);
              });
            },
          ),
          const SizedBox(height: 16),
          _MediumCompositionField(controller: _mediumCompositionController),
          const SizedBox(height: 16),
          _ExplantCountField(controller: _explantCountController),
          const SizedBox(height: 32),
          _CreateSubcultureButton(
            onPressed: () => _createSubculture(context),
          ),
        ],
      ),
    ),
  );

  void _createSubculture(BuildContext context) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final explantCount = int.tryParse(_explantCountController.text) ?? 0;
    if (explantCount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid explant count'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    context.read<CreateSubcultureCubit>().createSubculture(
      parentCultureId: widget.parentCulture.id,
      subcultureDate: _selectedDate,
      mediumComposition: _mediumCompositionController.text.trim(),
      explantCount: explantCount,
    );
  }

  String _formatDate(DateTime date) =>
      '${date.day}/${date.month}/${date.year}';
}

class _ParentCultureInfo extends StatelessWidget {
  const _ParentCultureInfo({required this.culture});

  final Culture culture;

  @override
  Widget build(BuildContext context) => Card(
    child: Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Parent Culture',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ID: ${culture.cultureId}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            'Species: ${culture.species}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            'Explant Type: ${culture.explantType}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    ),
  );
}

class _SubcultureDateField extends StatelessWidget {
  const _SubcultureDateField({
    required this.controller,
    required this.selectedDate,
    required this.onDateChanged,
  });

  final TextEditingController controller;
  final DateTime selectedDate;
  final ValueChanged<DateTime> onDateChanged;

  @override
  Widget build(BuildContext context) => TextFormField(
    controller: controller,
    readOnly: true,
    textCapitalization: TextCapitalization.none,
    keyboardType: TextInputType.none,
    textInputAction: TextInputAction.next,
    decoration: const InputDecoration(
      labelText: 'Subculture Date',
      hintText: 'Select the date of subculturing',
      suffixIcon: Icon(Icons.calendar_today),
      border: OutlineInputBorder(),
    ),
    validator: (value) {
      if (value == null || value.isEmpty) {
        return 'Please select a subculture date';
      }
      return null;
    },
    onTap: () => _selectDate(context),
  );

  Future<void> _selectDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null && picked != selectedDate) {
      onDateChanged(picked);
    }
  }
}

class _MediumCompositionField extends StatelessWidget {
  const _MediumCompositionField({required this.controller});

  final TextEditingController controller;

  @override
  Widget build(BuildContext context) => TextFormField(
    controller: controller,
    textCapitalization: TextCapitalization.sentences,
    keyboardType: TextInputType.text,
    textInputAction: TextInputAction.next,
    maxLines: 3,
    decoration: const InputDecoration(
      labelText: 'Medium Composition',
      hintText: 'Enter the medium composition for this subculture',
      border: OutlineInputBorder(),
    ),
    validator: (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Please enter the medium composition';
      }
      return null;
    },
  );
}

class _ExplantCountField extends StatelessWidget {
  const _ExplantCountField({required this.controller});

  final TextEditingController controller;

  @override
  Widget build(BuildContext context) => TextFormField(
    controller: controller,
    textCapitalization: TextCapitalization.none,
    keyboardType: TextInputType.number,
    textInputAction: TextInputAction.done,
    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
    decoration: const InputDecoration(
      labelText: 'Number of Explants',
      hintText: 'Enter the number of explants transferred',
      border: OutlineInputBorder(),
    ),
    validator: (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Please enter the number of explants';
      }
      final count = int.tryParse(value);
      if (count == null || count <= 0) {
        return 'Please enter a valid number greater than 0';
      }
      return null;
    },
  );
}

class _CreateSubcultureButton extends StatelessWidget {
  const _CreateSubcultureButton({required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) => SizedBox(
    width: double.infinity,
    child: ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: const Text('Create Subculture'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    ),
  );
}
