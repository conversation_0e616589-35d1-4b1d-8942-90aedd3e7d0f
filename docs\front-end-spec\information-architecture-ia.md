# Information Architecture (IA)

## Site Map / Screen Inventory

```mermaid
graph TD
    A[Launch Screen] --> B[Dashboard]
    A --> C[Onboarding Flow]

    B[Dashboard] --> B1[Active Cultures Overview]
    B --> B2[Recent Activity Feed]
    B --> B3[Next Actions Required]
    B --> B4[Quick Stats]

    B --> D[Cultures]
    D --> D1[Culture List/Grid]
    D --> D2[Create New Culture]
    D --> D3[Culture Detail]
    D3 --> D3a[Progress Timeline]
    D3 --> D3b[Photos & Notes]
    D3 --> D3c[Protocol Steps]
    D3 --> D3d[Environmental Data]

    B --> E[Protocols]
    E --> E1[Protocol Library]
    E --> E2[My Protocols]
    E --> E3[Community Protocols]
    E --> E4[Protocol Detail/Editor]

    B --> F[Equipment]
    F --> F1[Equipment Inventory]
    F --> F2[Maintenance Schedules]
    F --> F3[Equipment History]

    B --> G[Learning]
    G --> G1[Tutorial Library]
    G --> G2[Knowledge Base]
    G --> G3[Troubleshooting Guide]
    G --> G4[Community Forum/Q&A]

    B --> H[Profile]
    H --> H1[Account Settings]
    H --> H2[Data Export/Backup]
    H --> H3[Notifications]
    H --> H4[App Preferences]
```

## Navigation Structure

**Primary Navigation:** Bottom tab bar with 5 core sections (Dashboard, Cultures, Protocols, Learning, Profile)

**Secondary Navigation:** Context-sensitive top navigation within each section, utilizing back buttons and breadcrumbs for deep navigation paths. Enhanced with contextual shortcuts in Culture Detail screens for quick access to troubleshooting and related protocols.

**Breadcrumb Strategy:** Hierarchical breadcrumbs for multi-level sections (e.g., Cultures > Culture Detail > Progress Timeline), with tab persistence to maintain user's place when switching between sections.

**Cross-Section Integration:** Floating action buttons and contextual bridges connecting related content across sections (protocols accessible from culture work, learning resources from troubleshooting contexts).
