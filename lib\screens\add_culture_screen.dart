import 'dart:developer';

import 'package:drift/drift.dart' hide Column;
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../data/database/database.dart';
import '../data/models/culture_status.dart';

class AddCultureScreen extends StatefulWidget {
  const AddCultureScreen({super.key});

  @override
  State<AddCultureScreen> createState() => _AddCultureScreenState();
}

class _AddCultureScreenState extends State<AddCultureScreen> {
  final _formKey = GlobalKey<FormState>();
  final _speciesController = TextEditingController();
  final _explantTypeController = TextEditingController();
  final _sourcePlantIdController = TextEditingController();
  final _mediumCompositionController = TextEditingController();
  final _initialConditionsController = TextEditingController();

  DateTime _initiationDate = DateTime.now();
  bool _isLoading = false;

  late final CultureDatabase _database;

  @override
  void initState() {
    super.initState();
    _database = CultureDatabase();
  }

  @override
  void dispose() {
    _speciesController.dispose();
    _explantTypeController.dispose();
    _sourcePlantIdController.dispose();
    _mediumCompositionController.dispose();
    _initialConditionsController.dispose();
    _database.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Culture'),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildSpeciesField(),
              const SizedBox(height: 16),
              _buildExplantTypeField(),
              const SizedBox(height: 16),
              _buildSourcePlantIdField(),
              const SizedBox(height: 16),
              _buildInitiationDateField(),
              const SizedBox(height: 16),
              _buildMediumCompositionField(),
              const SizedBox(height: 16),
              _buildInitialConditionsField(),
              const SizedBox(height: 32),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpeciesField() => TextFormField(
    controller: _speciesController,
    textCapitalization: TextCapitalization.words,
    keyboardType: TextInputType.text,
    textInputAction: TextInputAction.next,
    decoration: const InputDecoration(
      labelText: 'Species/Variety *',
      hintText: 'Enter plant species or variety name',
      border: OutlineInputBorder(),
    ),
    validator: (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Species/Variety is required';
      }
      return null;
    },
  );

  Widget _buildExplantTypeField() => TextFormField(
    controller: _explantTypeController,
    textCapitalization: TextCapitalization.words,
    keyboardType: TextInputType.text,
    textInputAction: TextInputAction.next,
    decoration: const InputDecoration(
      labelText: 'Explant Type *',
      hintText: 'e.g., Leaf, Node, Shoot tip',
      border: OutlineInputBorder(),
    ),
    validator: (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Explant Type is required';
      }
      return null;
    },
  );

  Widget _buildSourcePlantIdField() => TextFormField(
    controller: _sourcePlantIdController,
    textCapitalization: TextCapitalization.characters,
    keyboardType: TextInputType.text,
    textInputAction: TextInputAction.next,
    decoration: const InputDecoration(
      labelText: 'Source Plant ID',
      hintText: 'Optional identifier for source plant',
      border: OutlineInputBorder(),
    ),
  );

  Widget _buildInitiationDateField() => InkWell(
    onTap: _selectInitiationDate,
    child: InputDecorator(
      decoration: const InputDecoration(
        labelText: 'Initiation Date *',
        border: OutlineInputBorder(),
        suffixIcon: Icon(Icons.calendar_today),
      ),
      child: Text(
        '${_initiationDate.day}/${_initiationDate.month}/${_initiationDate.year}',
        style: Theme.of(context).textTheme.bodyLarge,
      ),
    ),
  );

  Widget _buildMediumCompositionField() => TextFormField(
    controller: _mediumCompositionController,
    textCapitalization: TextCapitalization.sentences,
    keyboardType: TextInputType.multiline,
    textInputAction: TextInputAction.newline,
    maxLines: 3,
    decoration: const InputDecoration(
      labelText: 'Medium Composition',
      hintText: 'Describe the culture medium used',
      border: OutlineInputBorder(),
    ),
  );

  Widget _buildInitialConditionsField() => TextFormField(
    controller: _initialConditionsController,
    textCapitalization: TextCapitalization.sentences,
    keyboardType: TextInputType.multiline,
    textInputAction: TextInputAction.done,
    maxLines: 3,
    decoration: const InputDecoration(
      labelText: 'Initial Conditions',
      hintText: 'Describe initial culture conditions',
      border: OutlineInputBorder(),
    ),
  );

  Widget _buildSaveButton() => ElevatedButton(
    onPressed: _isLoading ? null : _saveCulture,
    child: _isLoading
        ? const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Text('Save Culture'),
  );

  Future<void> _selectInitiationDate() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _initiationDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      setState(() {
        _initiationDate = selectedDate;
      });
    }
  }

  Future<void> _saveCulture() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Generate unique Culture ID
      final cultureId = await _generateCultureId();

      // Create Culture entity
      final now = DateTime.now();
      final uuid = const Uuid();

      final culture = CulturesCompanion.insert(
        id: uuid.v4(),
        cultureId: cultureId,
        species: _speciesController.text.trim(),
        explantType: _explantTypeController.text.trim(),
        sourcePlantId: _sourcePlantIdController.text.trim().isEmpty
            ? const Value(null)
            : Value(_sourcePlantIdController.text.trim()),
        initiationDate: _initiationDate,
        mediumComposition: _mediumCompositionController.text.trim(),
        initialConditions: _initialConditionsController.text.trim().isEmpty
            ? const Value(null)
            : Value(_initialConditionsController.text.trim()),
        status: CultureStatus.healthy,
        createdAt: now,
        updatedAt: now,
      );

      // Save to database
      await _database.insertCulture(culture);

      log('Culture created successfully: $cultureId', name: 'AddCultureScreen');

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      log('Error creating culture: $e', name: 'AddCultureScreen');

      if (mounted) {
        _showErrorDialog('Failed to create culture: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<String> _generateCultureId() async {
    // Query existing cultures to find the highest ID number
    final cultures = await _database.getAllCultures();

    int maxNumber = 0;
    for (final culture in cultures) {
      final match = RegExp(r'C(\d+)').firstMatch(culture.cultureId);
      if (match != null) {
        final number = int.tryParse(match.group(1)!) ?? 0;
        if (number > maxNumber) {
          maxNumber = number;
        }
      }
    }

    // Generate next sequential ID
    final nextNumber = maxNumber + 1;
    return 'C${nextNumber.toString().padLeft(3, '0')}';
  }

  void _showErrorDialog(String message) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: SelectableText.rich(
          TextSpan(
            text: message,
            style: TextStyle(color: Theme.of(context).colorScheme.error),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
