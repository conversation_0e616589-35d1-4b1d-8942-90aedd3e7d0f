// This is a basic Flutter widget test for CultureStack app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:culture_stack/main.dart';

void main() {
  testWidgets('CultureStack app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const CultureStackApp());

    // Verify that the app loads with the main navigation
    expect(find.text('CultureStack'), findsOneWidget);
    expect(find.text('Welcome to CultureStack'), findsOneWidget);

    // Verify navigation tabs are present
    expect(find.text('Timeline'), findsOneWidget);
    expect(find.text('Calendar'), findsOneWidget);
    expect(find.text('Add'), findsOneWidget);
    expect(find.text('Library'), findsOneWidget);
    expect(find.text('Settings'), findsOneWidget);
  });

  testWidgets('Navigation between tabs works', (WidgetTester tester) async {
    await tester.pumpWidget(const CultureStackApp());

    // Tap on Calendar tab
    await tester.tap(find.text('Calendar'));
    await tester.pump();

    // Verify Calendar screen is displayed
    expect(find.text('Calendar View'), findsOneWidget);
    expect(find.text('Coming Soon'), findsOneWidget);
  });
}
