import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_stack/main.dart';

void main() {
  group('Navigation Integration Tests', () {
    testWidgets('should navigate between all tabs', (WidgetTester tester) async {
      await tester.pumpWidget(const CultureStackApp());

      // Verify initial state (Timeline tab)
      expect(find.text('Welcome to CultureStack'), findsOneWidget);

      // Navigate to Calendar tab
      await tester.tap(find.text('Calendar').last);
      await tester.pump();
      expect(find.text('Calendar View'), findsOneWidget);

      // Navigate to Add tab
      await tester.tap(find.text('Add').last);
      await tester.pump();
      expect(find.text('Add New Culture'), findsOneWidget);

      // Navigate to Library tab
      await tester.tap(find.text('Library').last);
      await tester.pump();
      expect(find.text('Culture Library'), findsOneWidget);

      // Navigate to Settings tab
      await tester.tap(find.text('Settings').last);
      await tester.pump();
      expect(find.textContaining('Configure app preferences'), findsOneWidget);

      // Navigate back to Timeline tab
      await tester.tap(find.text('Timeline').last);
      await tester.pump();
      expect(find.text('Welcome to CultureStack'), findsOneWidget);
    });

    testWidgets('should maintain state when switching tabs', (WidgetTester tester) async {
      await tester.pumpWidget(const CultureStackApp());

      // Start on Timeline
      expect(find.text('Welcome to CultureStack'), findsOneWidget);

      // Go to Calendar
      await tester.tap(find.text('Calendar').last);
      await tester.pump();
      expect(find.text('Calendar View'), findsOneWidget);

      // Go to Timeline and back to Calendar - should maintain state
      await tester.tap(find.text('Timeline').last);
      await tester.pump();
      await tester.tap(find.text('Calendar').last);
      await tester.pump();
      expect(find.text('Calendar View'), findsOneWidget);
    });

    testWidgets('should show coming soon messages for unimplemented features', (WidgetTester tester) async {
      await tester.pumpWidget(const CultureStackApp());

      final tabs = ['Calendar', 'Add', 'Library', 'Settings'];
      
      for (final tab in tabs) {
        await tester.tap(find.text(tab).last);
        await tester.pump();
        expect(find.text('Coming Soon'), findsOneWidget);
      }
    });
  });
}
