# User Flows

## Create First Culture Flow

**User Goal:** Successfully set up their first tissue culture batch with guidance

**Entry Points:** Dashboard "Create First Culture" CTA, Cultures section "New Culture" button, Onboarding flow completion

**Success Criteria:** Culture created with complete initial data, user understands next steps, follow-up notifications configured

### Flow Diagram

```mermaid
graph TD
    A[Entry Point] --> B{First Time User?}
    B -->|Yes| C[Show Culture Creation Tutorial]
    B -->|No| D[Direct to Creation Form]

    C --> E[Protocol Selection]
    D --> E

    E --> F{Has Preferred Protocol?}
    F -->|Yes| G[Load Selected Protocol]
    F -->|No| H[Browse Protocol Library]

    H --> I[Filter by Plant Type/Difficulty]
    I --> J[Select Recommended Protocol]
    J --> G

    G --> K[Culture Naming & Details]
    K --> L[Environmental Setup]
    L --> M[Initial Photos/Notes]
    M --> N[Review & Confirm]
    N --> O[Culture Created]

    O --> P[Setup Success Notification]
    P --> Q[Navigate to Culture Detail]
```

### Edge Cases & Error Handling:
- **No protocols available:** Guide to create basic protocol or use default
- **Missing required data:** Inline validation with helpful tooltips
- **Camera access denied:** Alternative text-only note entry
- **Save interruption:** Auto-save draft and recovery prompt

**Notes:** First-time users receive progressive disclosure of features, with advanced options hidden until confidence is built.

## Daily Culture Monitoring Flow

**User Goal:** Quickly check culture status, add observations, and identify required actions

**Entry Points:** Dashboard culture cards, push notification, scheduled reminder

**Success Criteria:** Status updated, photos/notes captured, next actions identified and scheduled

### Flow Diagram

```mermaid
graph TD
    A[Entry Point] --> B[Culture Overview Screen]
    B --> C{Any Alerts?}
    C -->|Yes| D[Show Alert Details]
    C -->|No| E[Normal Status View]

    D --> F[Address Alert Action]
    F --> G[Update Status]
    E --> G

    G --> H{Add Observation?}
    H -->|Yes| I[Quick Photo Capture]
    H -->|No| K[Review Next Actions]

    I --> J[Add Notes/Tags]
    J --> K

    K --> L{Actions Required?}
    L -->|Yes| M[Schedule Next Steps]
    L -->|No| N[Mark Check Complete]

    M --> O[Set Reminders]
    O --> N
    N --> P[Return to Dashboard/Culture List]
```

### Edge Cases & Error Handling:
- **Culture showing concerning symptoms:** Context-sensitive troubleshooting suggestions
- **Missed scheduled checks:** Highlight overdue items with catch-up guidance
- **Photo quality issues:** Retake prompts with lighting/focus tips
- **Data sync failure:** Offline mode with sync when connected

**Notes:** Optimized for speed with minimal taps, using smart defaults and predictive text for common observations.

## Additional Critical Flows

**Troubleshooting Crisis Flow:** Symptom identification → diagnostic questions → treatment options → implementation → follow-up monitoring

**Batch Culture Management Flow:** Multi-culture selection → batch operations → comparative analysis → group scheduling

**Community Engagement Flow:** Content creation/question → community response → discussion → knowledge integration → personal application

**Equipment Management Flow:** Equipment check → maintenance log → calibration/cleaning → validation → schedule next service

**Protocol Research & Adaptation Flow:** Search/filter → compare options → preview steps → clone & customize → test run → save personal version

## Cross-Flow Integration Patterns

- **Emergency Exits:** Quick access to troubleshooting from any screen
- **Context Preservation:** Return to exact state after seeking help
- **Progressive Complexity:** Simple → detailed views based on user expertise
- **Predictive Navigation:** Smart suggestions based on culture stage and history
