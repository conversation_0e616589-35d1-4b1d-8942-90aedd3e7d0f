# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CultureStack.BMAD is a plant tissue culture management application project that leverages the BMAD™ (Breakthrough Method of Agile AI-driven Development) framework. This is primarily a documentation and planning repository containing AI agent definitions, workflows, templates, and project briefs for developing a native Android application for plant tissue culture enthusiasts.

## Repository Structure

### Core BMAD Framework
- `.bmad-core/` - Contains the complete BMAD framework implementation
  - `agents/` - AI agent definitions (bmad-master, dev, pm, po, architect, analyst, qa, sm, ux-expert)
  - `tasks/` - Executable workflow files for various development tasks
  - `templates/` - YAML template files for documents (PRD, architecture, stories, etc.)
  - `checklists/` - Quality assurance and process checklists
  - `data/` - Knowledge base and reference materials
  - `workflows/` - Predefined workflow patterns for different project types

### Trae Integration
- `.trae/rules/` - Mirror of agent definitions for Trae AI integration
- Agent files are duplicated between `.bmad-core/agents/` and `.trae/rules/`

### Project Documentation
- `docs/brief.md` - Comprehensive project brief for CultureStack mobile app
- Project focuses on Android-native tissue culture management application

## Key BMAD Concepts

### Agent System
The repository contains specialized AI agents that work together:
- **bmad-master**: Universal task executor and method expert
- **dev**: Development implementation
- **pm**: Project management
- **po**: Product ownership
- **architect**: Technical architecture
- **analyst**: Business analysis
- **qa**: Quality assurance
- **sm**: Scrum management
- **ux-expert**: User experience design

### Agent Activation Pattern
Agents follow a specific activation pattern:
1. Read complete agent definition from their respective files
2. Load `bmad-core/core-config.yaml` (if present)
3. Greet user and run `*help` command
4. Load dependency files only when commanded (never pre-load)

### Command Structure
All agent commands use `*` prefix (e.g., `*help`, `*task`, `*create-doc`)

### Critical Workflow Rules
- Tasks with `elicit: true` require mandatory user interaction
- Dependency resolution maps to `.bmad-core/{type}/{name}` structure
- Agent customization fields override conflicting instructions
- Resources are loaded at runtime, never pre-loaded

## Working with Templates

Templates are YAML-driven and located in `.bmad-core/templates/`:
- `prd-tmpl.yaml` - Product Requirements Document
- `architecture-tmpl.yaml` - Architecture documentation
- `story-tmpl.yaml` - User story template
- Various specialized templates for brownfield/greenfield projects

## Task Execution

Tasks in `.bmad-core/tasks/` are executable workflows:
- `create-doc.md` - Document creation from templates
- `document-project.md` - Project documentation workflow
- `create-next-story.md` - Story creation process
- Tasks require step-by-step execution with user interaction

## Development Commands

Since this is primarily a documentation/planning repository, traditional build/test commands are not applicable. Instead, work focuses on:

### BMAD Agent Commands
```bash
# Activate bmad-master agent and use commands like:
*help                    # Show available commands
*create-doc {template}   # Create document from template
*task {task}            # Execute specific task
*execute-checklist      # Run quality checklists
*kb                     # Toggle knowledge base mode
```

### File Navigation
```bash
# Key directories to explore
ls .bmad-core/agents/     # Agent definitions
ls .bmad-core/tasks/      # Executable workflows
ls .bmad-core/templates/  # Document templates
ls docs/                  # Project documentation
```

## Project Context

This repository is for planning and designing "CultureStack" - a mobile Android application for plant tissue culture management. The actual development will happen in a separate repository using the documentation and workflows defined here.

## VSCode Configuration

The repository includes VSCode settings for:
- Chat agent integration (Copilot/GitHub)
- MCP discovery enabled
- Auto-fix capabilities
- Tool approval workflow

## Flutter Development Guidelines

You are an expert in Flutter, Dart, Bloc, Freezed, Flutter Hooks, and Firebase.

### MANDATORY Code Structure (ALWAYS enforce)
```dart
// File structure (top to bottom):
// 1. imports (dart: first, package: second, relative: last)
// 2. main exported widget
// 3. private subwidgets
// 4. helper functions
// 5. constants/static content
// 6. types/models

// Required imports order example:
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/user.dart';
```

### MANDATORY Naming Conventions (ALWAYS enforce)
- **Classes**: `PascalCase` (e.g., `UserProfileWidget`, `AuthenticationBloc`)
- **Functions/Methods**: `camelCase` starting with verb (e.g., `fetchUser()`, `validateEmail()`)
- **Variables**: `camelCase` with auxiliary verbs (e.g., `isLoading`, `hasError`, `canSubmit`)
- **Files**: `snake_case` (e.g., `user_profile_widget.dart`, `auth_bloc.dart`)
- **Constants**: `lowerCamelCase` (e.g., `defaultPadding`, `maxRetryAttempts`)
- **Enums**: `PascalCase` with `@JsonValue(int)` for database storage

### MANDATORY Widget Patterns (ALWAYS enforce)
```dart
// ALWAYS use const constructors when possible
class MyWidget extends StatelessWidget {
  const MyWidget({super.key, required this.data});
  final String data;

  @override
  Widget build(BuildContext context) =>
    // ALWAYS use arrow syntax for single expressions
    Container(child: Text(data)); // ALWAYS trailing comma
}

// ALWAYS create private widget classes, NEVER widget methods
class _PrivateSubWidget extends StatelessWidget {
  const _PrivateSubWidget({required this.value});
  final int value;
  @override
  Widget build(BuildContext context) => Text('$value');
}
```

### MANDATORY State Management (ALWAYS enforce)
```dart
// Cubit for simple state, Bloc for complex events
class UserCubit extends Cubit<UserState> {
  UserCubit() : super(const UserState.initial());

  Future<void> fetchUser() async {
    emit(const UserState.loading());
    try {
      final user = await _repository.getUser();
      emit(UserState.loaded(user));
    } catch (e) {
      emit(UserState.error(e.toString()));
    }
  }
}

// ALWAYS use Freezed for state classes
@freezed
class UserState with _$UserState {
  const factory UserState.initial() = _Initial;
  const factory UserState.loading() = _Loading;
  const factory UserState.loaded(User user) = _Loaded;
  const factory UserState.error(String message) = _Error;
}

// ALWAYS use BlocBuilder for UI state, BlocListener for side effects
BlocBuilder<UserCubit, UserState>(
  builder: (context, state) => state.when(
    initial: () => const SizedBox.shrink(),
    loading: () => const CircularProgressIndicator(),
    loaded: (user) => UserWidget(user: user),
    error: (message) => SelectableText.rich(
      TextSpan(
        text: 'Error: $message',
        style: TextStyle(color: Theme.of(context).colorScheme.error),
      ),
    ),
  ),
)
```

### MANDATORY Error Handling (ALWAYS enforce)
```dart
// NEVER use SnackBars for errors, ALWAYS use SelectableText.rich
Widget buildError(String message) => SelectableText.rich(
  TextSpan(
    text: 'Error: $message',
    style: TextStyle(color: Theme.of(context).colorScheme.error),
  ),
);

// ALWAYS handle Firebase exceptions specifically
try {
  await FirebaseAuth.instance.signInWithEmailAndPassword(
    email: email,
    password: password,
  );
} on FirebaseAuthException catch (e) {
  switch (e.code) {
    case 'user-not-found':
      emit(AuthState.error('No user found for that email.'));
    case 'wrong-password':
      emit(AuthState.error('Wrong password provided.'));
    default:
      emit(AuthState.error('Authentication failed: ${e.message}'));
  }
} catch (e) {
  emit(AuthState.error('Unexpected error: $e'));
}
```

### MANDATORY Firebase Patterns (ALWAYS enforce)
```dart
// ALWAYS include these fields in Firestore documents
@JsonSerializable(fieldRename: FieldRename.snake)
class FirestoreModel {
  const FirestoreModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.isDeleted = false,
  });

  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDeleted;

  // ALWAYS use read-only fields for server timestamps
  @JsonKey(includeFromJson: true, includeToJson: false)
  final DateTime? serverTimestamp;
}

// ALWAYS limit and index Firestore queries
Query<Map<String, dynamic>> getUsersQuery() => FirebaseFirestore.instance
  .collection('users')
  .where('isDeleted', isEqualTo: false)
  .orderBy('createdAt', descending: true)
  .limit(50); // ALWAYS limit results
```

### MANDATORY Performance Rules (ALWAYS enforce)
- **ALWAYS** use `const` constructors where possible
- **ALWAYS** use `ListView.builder` for lists, NEVER `ListView(children: [])`
- **ALWAYS** use `AssetImage` for local images, `cached_network_image` for remote
- **ALWAYS** include `errorBuilder` in `Image.network`
- **ALWAYS** prefer `StatelessWidget` over `StatefulWidget`
- **ALWAYS** break deep widget trees into smaller components (max 3-4 levels)

### MANDATORY Code Style (ALWAYS enforce)
- **Line length**: Maximum 80 characters
- **Trailing commas**: ALWAYS add for multi-parameter functions/constructors
- **Arrow syntax**: ALWAYS use for single expressions (`=> expression`)
- **Debugging**: ALWAYS use `log()`, NEVER `print()`
- **Navigation**: ALWAYS use `GoRouter` or `auto_route`, NEVER `Navigator.push`

### MANDATORY TextField Configuration (ALWAYS enforce)
```dart
TextField(
  textCapitalization: TextCapitalization.words, // ALWAYS specify
  keyboardType: TextInputType.emailAddress,     // ALWAYS specify
  textInputAction: TextInputAction.next,        // ALWAYS specify
  // ... other properties
)
```

### MANDATORY Code Generation Workflow (ALWAYS enforce)
After modifying any `@freezed` or `@JsonSerializable` classes:
```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

### MANDATORY Theme Usage (ALWAYS enforce)
```dart
// NEVER use deprecated theme properties
Text(
  'Title',
  style: Theme.of(context).textTheme.titleLarge, // NOT headline6
)
Text(
  'Subtitle',
  style: Theme.of(context).textTheme.headlineSmall, // NOT headline5
)
```

### CHECKLIST: Before Submitting Code
- [ ] All widgets use `const` constructors where possible
- [ ] No `print()` statements (use `log()`)
- [ ] Trailing commas on multi-parameter functions
- [ ] Proper error handling with `SelectableText.rich`
- [ ] State management follows Bloc/Cubit patterns
- [ ] Firebase queries are limited and indexed
- [ ] No deep widget nesting (max 3-4 levels)
- [ ] Build runner executed after model changes
- [ ] Navigation uses GoRouter, not Navigator
- [ ] TextField inputs properly configured

**VIOLATION REMINDER**: If you see me violating any "ALWAYS" rule, immediately point it out so I can correct it.

## Important Notes

- This is a planning/documentation repository, not a code implementation
- Agent files should not be modified without understanding BMAD framework
- Templates and tasks follow specific YAML/Markdown formats
- Project brief in `docs/brief.md` contains complete product specification
- Agent activation must follow the prescribed sequence for proper operation
- When implementing the actual CultureStack mobile app, follow the Dart/Flutter guidelines above