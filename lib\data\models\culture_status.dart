import 'package:json_annotation/json_annotation.dart';

/// Enum representing the status of a culture
@JsonEnum(valueField: 'value')
enum CultureStatus {
  healthy(0, 'Healthy'),
  contaminated(1, 'Contaminated'),
  readyForTransfer(2, 'Ready for Transfer'),
  inRooting(3, 'In Rooting'),
  acclimatizing(4, 'Acclimatizing'),
  completed(5, 'Completed'),
  disposed(6, 'Disposed');

  const CultureStatus(this.value, this.displayName);

  final int value;
  final String displayName;

  /// Get CultureStatus from integer value
  static CultureStatus fromValue(int value) {
    return CultureStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CultureStatus.healthy,
    );
  }
}
