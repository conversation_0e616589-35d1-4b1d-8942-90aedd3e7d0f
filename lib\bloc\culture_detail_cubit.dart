import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../data/database/database.dart';

part 'culture_detail_cubit.freezed.dart';
part 'culture_detail_state.dart';

class CultureDetailCubit extends Cubit<CultureDetailState> {
  CultureDetailCubit(this._database) : super(const CultureDetailState.initial());

  final CultureDatabase _database;
  StreamSubscription<List<Subculture>>? _subculturesSubscription;

  Future<void> loadCultureDetail(String cultureId) async {
    emit(const CultureDetailState.loading());

    try {
      // Load culture details
      final culture = await _database.getCultureById(cultureId);
      if (culture == null) {
        emit(const CultureDetailState.error('Culture not found'));
        return;
      }

      // Load subcultures for this culture
      final subcultures = await _database.getSubculturesForCulture(cultureId);
      
      log('Loaded culture ${culture.cultureId} with ${subcultures.length} subcultures', 
          name: 'CultureDetailCubit');

      emit(CultureDetailState.loaded(culture, subcultures));

      // Watch for subculture changes
      _subculturesSubscription?.cancel();
      _subculturesSubscription = _watchSubcultures(cultureId);
    } catch (error) {
      log('Error loading culture detail: $error', name: 'CultureDetailCubit');
      emit(CultureDetailState.error(error.toString()));
    }
  }

  StreamSubscription<List<Subculture>> _watchSubcultures(String cultureId) {
    return Stream.periodic(const Duration(seconds: 1))
        .asyncMap((_) => _database.getSubculturesForCulture(cultureId))
        .listen(
          (subcultures) {
            final currentState = state;
            if (currentState is _Loaded) {
              emit(CultureDetailState.loaded(currentState.culture, subcultures));
            }
          },
          onError: (error) {
            log('Error watching subcultures: $error', name: 'CultureDetailCubit');
          },
        );
  }

  Future<void> refreshCultureDetail(String cultureId) async {
    try {
      final culture = await _database.getCultureById(cultureId);
      if (culture == null) {
        emit(const CultureDetailState.error('Culture not found'));
        return;
      }

      final subcultures = await _database.getSubculturesForCulture(cultureId);
      emit(CultureDetailState.loaded(culture, subcultures));
    } catch (error) {
      log('Error refreshing culture detail: $error', name: 'CultureDetailCubit');
      emit(CultureDetailState.error(error.toString()));
    }
  }

  @override
  Future<void> close() {
    _subculturesSubscription?.cancel();
    return super.close();
  }
}
